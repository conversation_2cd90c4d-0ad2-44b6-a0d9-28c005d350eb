regulatory-model-number
syscfg/RMd#/0x20
#address-cells
AAPL,phandle
country-of-origin
syscfg/Coor
config-number
syscfg/CFG#/0x40,zeroes/0x40
serial-number
syscfg/SrNm/0x20,zeroes/0x20
target-type
J313
platform-name
name
device-tree
mlb-serial-number
syscfg/MLB#/0x20,zeroes/0x20
secure-root-prefix
manufacturer
Apple Inc.
region-info
syscfg/Regn/0x20,zeroes/0x20
target-sub-type
J313AP
compatible
J313<PERSON>
MacBookAir10,1
AppleARM
model-number
syscfg/Mod#/0x20,zeroes/0x20
time-stamp
Thu Jun 12 23:34:53 PDT 2025
clock-frequency
model
MacBookAir10,1
model-config
syscfg/MdlC
device-tree-tag
EmbeddedDeviceTrees-11156.0.126
device_type
bootrom
#size-cells
mix-n-match-prevention-status
effective-security-mode-ap
crypto-hash-method
sha2-384
secure-boot
gid-aes-key
max-env-var-data-size
die-id
software-bundle-version
syscfg/SBVr/0x10,zeroes/0x10
repair-environment
image4-supported
repair-status
syscfg/rpcp
software-behavior
syscfg/SwBh/0x10,zeroes/0x10
effective-production-status-sep
disable-transport-rm
development-cert
mac-address-wifi0
macaddr/wifiaddr,syscfg/WMac/6
marketing-hardware-behavior
syscfg/MkBH
isp-horizon
max-env-partition
mac-address-ethernet1
macaddr/ethaddr1,syscfg/EMc2/6
system-trusted
display-rotation
amfi-allows-trust-cache-load
coverglass-color
syscfg/CLCG
name
chosen
sidp-rom-manifest-hash
sepfw-load-at-boot
AAPL,phandle
enable-sep-rm
dram-vendor-id
display-boot-rotation
protected-data-access
allow-whitelist-disable
certificate-security-mode
host-os-information
disable-av-content-protection
dram-vendor
mac-address-bluetooth0
macaddr/btaddr,syscfg/BMac/6
nvram-proxy-data
uid-aes-key
production-cert
non-apple-or-untrusted-code
ephemeral-storage
mac-address-ethernet0
macaddr/ethaddr,syscfg/EMac/6
debug-enabled
consistent-debug-root
enclosure-material
syscfg/EnMt
effective-production-status-ap
marketing-software-behavior
syscfg/MkBS
nvram-current-bank
security-downgradable
bootp-response
image4-cert-type
backing-glass-material
syscfg/BGMt
cl4-entropy
chip-id
darwinos-security-environment
firmware-version
unique-device-id
effective-security-mode-sep
display-scale
random-seed
certificate-production-status
apfs-preboot-uuid
darwinos-userspace-reboot
allowed-boot-args
trace,trace_wake,kperf,-x,-v,trm_enabled,trace_typefilter,nox86exec
csr-allow-device-configuration
recovery-snag-key-pressed
use-recovery-securityd
allow-ap-nonce-retrieval
nvram-bank-size
repair-history
syscfg/RCHL
boot-nonce
enable-user-rm
nvram-bank-count
cover-glass-material
syscfg/CGMt
max-env-var-name-size
osenvironment
#address-cells
amfi-exec-req-tc
disable-accessory-firmware
boot-manifest-hash
display-corner-radius
boot-type
local
board-id
unique-chip-id
backing-color
syscfg/CLBG
housing-color
syscfg/CLHS
UnusedIntegerProperty10
UnusedBooleanProperty3
UnusedIntegerProperty6
UnusedBooleanProperty18
UnusedBooleanProperty11
UnusedIntegerProperty15
UnusedIntegerProperty2
UnusedBooleanProperty6
UnusedIntegerProperty9
UnusedStringProperty2
UnusedBooleanProperty16
UnusedStringProperty5
UnusedIntegerProperty13
UnusedBooleanProperty2
UnusedIntegerProperty5
UnusedBooleanProperty9
UnusedStringProperty8
UnusedBooleanProperty14
UnusedIntegerProperty18
UnusedIntegerProperty1
UnusedIntegerProperty11
UnusedBooleanProperty5
UnusedIntegerProperty8
UnusedStringProperty0
UnusedBooleanProperty19
AAPL,phandle
UnusedBooleanProperty12
UnusedIntegerProperty16
UnusedStringProperty3
UnusedBooleanProperty1
UnusedIntegerProperty4
UnusedBooleanProperty8
name
manifest-properties
UnusedStringProperty6
UnusedBooleanProperty17
UnusedBooleanProperty10
UnusedIntegerProperty0
UnusedIntegerProperty14
UnusedBooleanProperty4
UnusedIntegerProperty7
UnusedStringProperty9
UnusedBooleanProperty15
UnusedIntegerProperty19
UnusedStringProperty1
UnusedIntegerProperty12
UnusedBooleanProperty0
UnusedBooleanProperty7
UnusedIntegerProperty3
UnusedStringProperty4
UnusedBooleanProperty13
UnusedIntegerProperty17
UnusedStringProperty7
UnusedIntegerProperty10
UnusedBooleanProperty3
UnusedIntegerProperty6
UnusedBooleanProperty18
UnusedBooleanProperty11
UnusedIntegerProperty15
UnusedIntegerProperty2
UnusedBooleanProperty6
UnusedIntegerProperty9
UnusedStringProperty2
UnusedBooleanProperty16
UnusedStringProperty5
UnusedIntegerProperty13
UnusedBooleanProperty2
UnusedIntegerProperty5
UnusedBooleanProperty9
UnusedStringProperty8
UnusedBooleanProperty14
UnusedIntegerProperty18
UnusedIntegerProperty1
UnusedIntegerProperty11
UnusedBooleanProperty5
UnusedIntegerProperty8
UnusedStringProperty0
UnusedBooleanProperty19
AAPL,phandle
UnusedBooleanProperty12
UnusedIntegerProperty16
UnusedStringProperty3
UnusedBooleanProperty1
UnusedIntegerProperty4
UnusedBooleanProperty8
name
manifest-object-properties
UnusedStringProperty6
UnusedBooleanProperty17
UnusedBooleanProperty10
UnusedIntegerProperty0
UnusedIntegerProperty14
UnusedBooleanProperty4
UnusedIntegerProperty7
UnusedStringProperty9
UnusedBooleanProperty15
UnusedIntegerProperty19
UnusedStringProperty1
UnusedIntegerProperty12
UnusedBooleanProperty0
UnusedBooleanProperty7
UnusedIntegerProperty3
UnusedStringProperty4
UnusedBooleanProperty13
UnusedIntegerProperty17
UnusedStringProperty7
name
boot-object-manifests
AAPL,phandle
name
secure-boot-hashes
AAPL,phandle
name
dynamic-object-map
AAPL,phandle
MemoryMapReserved-14
MemoryMapReserved-5
MemoryMapReserved-11
MemoryMapReserved-0
MemoryMapReserved-6
AAPL,phandle
MemoryMapReserved-15
MemoryMapReserved-1
MemoryMapReserved-7
MemoryMapReserved-12
MemoryMapReserved-2
MemoryMapReserved-8
MemoryMapReserved-3
MemoryMapReserved-9
MemoryMapReserved-10
MemoryMapReserved-13
MemoryMapReserved-4
name
memory-map
kernel-only
true
name
carveout-memory-map
AAPL,phandle
name
lock-regs
AAPL,phandle
name
amcc
AAPL,phandle
name
amcc-ctrr-a
AAPL,phandle
name
amcc-ctrr-b
AAPL,phandle
name
amcc-ctrr-c
AAPL,phandle
name
amcc-ctrr-d
AAPL,phandle
load-kernel-start
debug-wait-start
AAPL,phandle
name
iBoot
start-time
populate-registry-time
name
memory-report
AAPL,phandle
name
zeroization-report
AAPL,phandle
name
asmb
AAPL,phandle
name
options
AAPL,phandle
#address-cells
#size-cells
name
cpus
AAPL,phandle
AAPL,phandle
cpu-id
acc-impl-reg
no-aic-ipi-required
l2-cache-size
function-error_handler
HrrE
cpu-uttdbg-reg
interrupt-parent
name
cpu0
l2-cache-id
compatible
apple,icestorm
ARM,v8
interrupts
reg-private
state
waiting
function-enable_core
eroC
cluster-id
cpu-impl-reg
function-cpu_idle
Iupccoresight-reg
device_type
cpm-impl-reg
cluster-type
AAPL,phandle
cpu-id
acc-impl-reg
no-aic-ipi-required
l2-cache-size
function-error_handler
HrrE
cpu-uttdbg-reg
interrupt-parent
name
cpu1
l2-cache-id
compatible
apple,icestorm
ARM,v8
interrupts
reg-private
state
waiting
function-enable_core
eroC
cluster-id
cpu-impl-reg
function-cpu_idle
Iupccoresight-reg
device_type
cpm-impl-reg
cluster-type
AAPL,phandle
cpu-id
acc-impl-reg
no-aic-ipi-required
l2-cache-size
function-error_handler
HrrE
cpu-uttdbg-reg
interrupt-parent
name
cpu2
l2-cache-id
compatible
apple,icestorm
ARM,v8
interrupts
reg-private
state
waiting
function-enable_core
eroC
cluster-id
cpu-impl-reg
function-cpu_idle
Iupccoresight-reg
device_type
cpm-impl-reg
cluster-type
AAPL,phandle
cpu-id
acc-impl-reg
no-aic-ipi-required
l2-cache-size
function-error_handler
HrrE
cpu-uttdbg-reg
interrupt-parent
name
cpu3
l2-cache-id
compatible
apple,icestorm
ARM,v8
interrupts
reg-private
state
waiting
function-enable_core
eroC
cluster-id
cpu-impl-reg
function-cpu_idle
Iupccoresight-reg
device_type
cpm-impl-reg
cluster-type
AAPL,phandle
cpu-id
acc-impl-reg
no-aic-ipi-required
l2-cache-size
function-error_handler
HrrE
cpu-uttdbg-reg
interrupt-parent
name
cpu4
l2-cache-id
compatible
apple,firestorm
ARM,v8
interrupts
reg-private
state
waiting
function-enable_core
eroC
cluster-id
cpu-impl-reg
function-cpu_idle
Iupccoresight-reg
device_type
cpm-impl-reg
cluster-type
AAPL,phandle
cpu-id
acc-impl-reg
no-aic-ipi-required
l2-cache-size
function-error_handler
HrrE
cpu-uttdbg-reg
interrupt-parent
name
cpu5
l2-cache-id
compatible
apple,firestorm
ARM,v8
interrupts
reg-private
state
waiting
function-enable_core
eroC 
cluster-id
cpu-impl-reg
function-cpu_idle
Iupccoresight-reg
device_type
cpm-impl-reg
cluster-type
AAPL,phandle
cpu-id
acc-impl-reg
no-aic-ipi-required
l2-cache-size
function-error_handler
HrrE
cpu-uttdbg-reg
interrupt-parent
name
cpu6
l2-cache-id
compatible
apple,firestorm
ARM,v8
interrupts
reg-private
state
waiting
function-enable_core
eroC@
cluster-id
cpu-impl-reg
function-cpu_idle
Iupccoresight-reg
device_type
cpm-impl-reg
cluster-type
AAPL,phandle
cpu-id
acc-impl-reg
no-aic-ipi-required
l2-cache-size
function-error_handler
HrrE
cpu-uttdbg-reg
interrupt-parent
name
cpu7
l2-cache-id
compatible
apple,firestorm
ARM,v8
interrupts
reg-private
state
waiting
function-enable_core
eroC
cluster-id
cpu-impl-reg
function-cpu_idle
Iupccoresight-reg
device_type
cpm-impl-reg
cluster-type
dcp0
/arm-io/dcp
name
aliases
dcpext0
/arm-io/dcpext
AAPL,phandle
name
memory
AAPL,phandle
device_type
memory
name
pram
AAPL,phandle
device_type
pram
name
vram
AAPL,phandle
device_type
vram
name
socd-trace-ram
AAPL,phandle
device_type
socd-trace-ram
AAPL,phandle
hmac-reg-base
pmgr-reg-base
name
hibernate
pmgr-aes-offset
device_type
hibernate
default-options
function-pcie_port_control
CtrPC
function-reg_on
4WKpd0Pg
name
amfm
AAPL,phandle
device_type
amfm
compatible
arm-io,t8103
clock-frequencies
soc-generation
chip-revision
AAPL,phandle
iommu-present
acc-impl
device_type
t8103-io
#size-cells
ranges
#address-cells
usbphy-frequency
function-clock_gate
Gklcfunction-power_gate
Grwpname
arm-io
cpm-impl
compatible
spi-1,spimc
function-spi_cs0
lluninterrupt-parent
interrupts
dma-channels
clock-gates
clock-ids
device_type
#size-cells
AAPL,phandle
#address-cells
spi-version
dma-parent
name
spi2
compatible
biosensor,mesa
mesaType
interrupt-parent
interrupts
scan-timer-reset-time
function-hid_event_dispatch
DtuBsensor-id
device_type
mesa
time-between-scans
AAPL,phandle
function-mesa_pwr
OIPGl
power-on-delay
max-scan-time
spi-frequency
power-off-delay
name
mesa
compatible
spi-1,spimc
function-spi_cs0
OIPG1
interrupt-parent
interrupts
dma-channels
clock-gates
clock-ids
device_type
#size-cells
AAPL,phandle
#address-cells
spi-version
dma-parent
name
spi3
compatible
hid-transport,spi
hid-merge-personality
aud-early-boot-critical
interrupt-parent
image-tag
fdpiinterrupts
AAPL,phandle
device_type
bootloader-type
HIDDevice
kblang-calibration
syscfg/ksku
reset-sequence
function-spi_en
function-enable_cs
OIPG1
function-spi_en
OIPG
name
compatible
spi-1,spimc
function-spi_cs0
OIPG
interrupt-parent
interrupts
clock-gates
clock-ids
AAPL,phandle
device_type
#size-cells
#address-cells
spi-version
name
spi4
AAPL,phandle
firmware
name
dp855
compatible
parade,DP855
device_type
tcon
repeated-start
device_type
lcd-i2c-component
protection
interface
verify
AAPL,phandle
name
lcd-pmicwp
offset-size
repeated-start
device_type
lcd-i2c-component
protection
interface
verify
AAPL,phandle
name
lcd-pmic
offset-size
repeated-start
device_type
lcd-i2c-component
protection
interface
verify
AAPL,phandle
name
lcd-sswp
offset-size
repeated-start
device_type
lcd-i2c-component
protection
interface
verify
AAPL,phandle
name
lcd-ss
offset-size
name
tcon-registers
AAPL,phandle
device_type
tcon-registers
offset-size
hi-z
data-pol-inv
tx-bit-order
AAPL,phandle
multi-io-bit-order
verify
protection
device_type
lcd-spi-component
mode
multi-io
interface
rx-bit-order
name
lcd-eeprom
gpio-iic_scl
compatible
i2c,t8101
i2c,s5l8940x
iic,soft
gpio-iic_sda
interrupt-parent
interrupts
clock-gates
clock-ids
#address-cels
device_type
#size-cells
AAPL,phandle
function-device_reset
TSRA(
name
i2c1
private
compatible
audio-control,tas5770
interrupt-parent
interrupts
speaker-config
bop-config
AAPL,phandle
device_type
audio-control
iboot-audio-volume
function-reset
OIPG
speaker-protection
external-power-provider
name
audio-tas5770L-spkr-l
speaker1
gpio-iic_scl
compatible
i2c,t8101
i2c,s5l8940x
iic,soft
gpio-iic_sda
interrupt-parent
interrupts
clock-gates
clock-ids
#address-cels
device_type
#size-cells
AAPL,phandle
function-device_reset
TSRA)
name
i2c2
name
atcrt0
compatible
atcrt
AAPL,phandle
name
atcrt1
compatible
atcrt
AAPL,phandle
gpio-iic_scl
compatible
i2c,t8101
i2c,s5l8940x
iic,soft
gpio-iic_sda
interrupt-parent
interrupts
clock-gates
clock-ids
#address-cels
device_type
#size-cells
AAPL,phandle
function-device_reset
TSRA*
name
i2c3
private
samplerate-default
samplerate-subset
compatible
audio-control,cs42l83
PowerOffAtSleep
interrupt-parent
interrupts
function-codecinput_master
1DUAc2pamic-config
enable-ringsense
device_type
audio-control
function-reset
OIPG
ringsense-inverted
AAPL,phandle
external-power-provider
bits-per-sample-subset
name
audio-codec-output
function-codecinput_active
Rs2i3nip
AAPL,phandle
name
audio-tas5770L-spkr-r
compatible
audio-control,tas5770
device_type
audio-control
interrupt-parent
compatible
fpwm,t8101
fpwm,s5l8920x
AAPL,phandle
interrupts
clock-gates
device_type
fpwm
name
fpwm
name
kbd-backlight
AAPL,phandle
device_type
fpwm
nits-to-pwm-percentage-part2
default-hz
nits-to-pwm-percentage-part1
AAPL,phandle
external-power-provider
dma-channels
compatible
alc,t8103
function-aop-device-control
ltCgfunction-admac_powerswitch
CSPaalc-number
dma-parent
device_type
name
alc0
function-aop-device-control-id
iaph
private
device-uid
Digital Mic
input-data-selectors
1imi2imi3imicompatible
audio-data,external
AAPL,phandle
device_type
leap-audio-data
data-sources
a2pa
Leap Mic
audio-stream-formatter
paeldefault-input-data-selectors
1imi2imi3imidevice-name
Digital Mic
name
audio-leap-mic
module-instance
shikoku
pcie-throttle-firmware-load
interrupt-parent
wifi-rfem-info
syscfg/RFEM
interrupts
wifi-calibration-msf
syscfg/WCAL
wlan.nan.enabled
AAPL,phandle
device_type
wlan
amfm-managed-port-control
function-sac
CCAS0dcl1dclwlan.lowlatency
local-mac-address
macaddr/wifiaddr,syscfg/WMac/6,zeroes/6
name
wlan
wifi-antenna-sku-info
syscfg/WSKU
compatible
uart-1,samsung
clock-ids
interrupt-parent
interrupts
dma-channels
clock-gates
AAPL,phandle
device_type
uart
function-rts
OIPG
function-tx
OIPG
dma-types
dma-parent
uart-version
name
uart2
wlan.uart.baudrate
name
wlan-debug
AAPL,phandle
device_type
wlan-debug
AAPL,phandle
voice-record
bluetooth-tx-calibration
syscfg/BTTx/0x14
local-mac-address
macaddr/btaddr,syscfg/BMac/6,zeroes/6
bluetooth-taurus-calibration-bf
syscfg/BTBF
bluetooth-taurus-cal-tmp1
syscfg/BTm1
supported-profiles
function-int_timestamp
tCIA
name
bluetooth
vendor-id
interrupt-parent
transport-encoding
coex
compatible
bluetooth,n88
function-bootstrap_lock
KCOLinterrupts
product-id
bootstrap-delay
device_type
bluetooth
bluetooth-taurus-cal-tmp2
syscfg/BTm2
bluetooth-rx-calibration
syscfg/BTRx/0xce
bluetooth-taurus-calibration
syscfg/BCAL
power-gates
domain-id
clock-gates
#address-cells
AAPL,phandle
msi-vector-offset
pci-aer-correctable
bus-range
lane-cfg
function-debug_gpio
OIPG
name
apcie
interrupt-parent
dev-range
compatible
apcie,t8103
interrupts
msi-address
pci-aer-uncorrectable
ranges
#ports
link-state-power
device_type
#size-cells
msi-parent-controller
#msi-vectors
pci-l1pm-control
#msi-vectors
#address-cells
AAPL,phandle
AAPL,unit-string
00000000
function-clkreq
OIPG
built-in
manual-enable
maximum-link-speed
pci-max-payload-size
function-perst
OIPG
name
pci-bridge0
t-refclk-to-perst
function-dart_force_active
tcaFcompatible
apcie-bridge
function-dart_request_sid
qeRSdefault-apcie-options
manual-enable-s2r
apcie-port
perst-to-config
function-dart_release_sid
leRSfunction-dart_self
fleSmsi-vector-base
#size-cells
pci-aspm-default
compatible
wlan-pcie,bcm4378
wlan-pcie,bcm
built-in
iommu-parent
AAPL,unit-string
00000000
AAPL,phandle
pci-max-latency
device_type
pcie-device
#size-cells
#address-cells
pci-l1pm-control
name
wlan
pci-max-latency
compatible
wlan-pcie,bcm4378
wlan-pcie,bcm
AAPL,unit-string
00000001
pci-aspm-default
AAPL,phandle
built-in
iommu-parent
#address-cells
#size-cells
device_type
pcie-device
name
bluetooth-pcie
manual-availability
compatible
dart,t8020
page-size
interrupt-parent
interrupts
vm-offset
AAPL,phandle
error-reflector
device_type
dart
dart-options
vm-base
diag-config
instance
TRADDART
tlb-invalid-refetch
protection-granularity
name
dart-apcie0
vm-size
AAPL,phandle
name
mapper-apcie0-wlan
compatible
iommu-mapper
device_type
dart-mapper
AAPL,phandle
name
mapper-apcie0-bt
compatible
iommu-mapper
device_type
dart-mapper
power-gates
domain-id
apciec-piodma-sid
clock-gates
#address-cells
AAPL,phandle
msi-vector-offset
pci-aer-correctable
bus-range
acio-parent
name
apciec0
interrupt-parent
port-type
xhci-interrupt-rmbs
dev-range
compatible
apciec,t8103
interrupts
msi-address
apciec-piodma
pci-aer-uncorrectable
ranges
port-number
link-state-power
device_type
pci-c
#size-cells
msi-parent-controller
#msi-vectors
#size-cells
IOPCITunnelLinkChange
PCI-Thunderbolt
AAPL,phandle
#address-cells
AAPL,unit-string
00000000
device-protection-granularity
name
pcic0-bridge
function-dart_force_active
tcaFcompatible
pciec-bridge
function-dart_request_sid
qeRSdefault-apcie-options
pci-ignore-linkstatus
marvel-wa-viddids
msi-for-bridges
AAPL,slot-name
Slot-0
function-dart_release_sid
leRSfunction-dart_self
fleS#msi-vectors
msi-vector-base
protection-granularity
AAPL,phandle
instance
TRADDART
tlb-invalid-refetch
dart-options
sid-count-a0
name
dart-apciec0
sids-a0
interrupt-parent
vm-reserve
compatible
dart,t8020
page-size
interrupts
diag-config
manual-availability
dead-mappings
vm-base
sid-count
relaxed-rw-protections
error-reflector
device_type
dart
sids
AAPL,phandle
name
mapper-apciec0-piodma
compatible
iommu-mapper
device_type
dart-mapper
piodma-max-transfer-size
compatible
pciec-apiodma,t8103
iommu-parent
interrupt-parent
interrupts
piodma-num-address-bits
AAPL,phandle
piodma-max-segment-size
device_type
apciec0-piodma
piodma-byte-alignment
piodma-id
piodma-fifo-size
name
apciec0-piodma
thunderbolt-drom
Apple Inc.
clock-gates
AAPL,phandle
atc-phy-parent
function-dock_parent
Pccaportmap
link-speed-default
gpio-lstx
port-defaults
+revision
iommu-parent
function-pcie_port_control
CtrPJ
name
acio0
function-dart_force_active
tcaFinterrupt-parent
link-width-default
port-type
compatible
acio
interrupts
vid-did
 acio-cpu
spec-version
port-number
device_type
acio
power-gates
bypass
compatible
dart,t8020
manual-availability
interrupt-parent
interrupts
page-size
AAPL,phandle
error-reflector
device_type
dart
dart-options
diag-config
instance
TRADDART
sids
tlb-invalid-refetch
name
dart-acio0
AAPL,phandle
name
mapper-acio0
compatible
iommu-mapper
device_type
dart-mapper
interrupt-parent
compatible
iop,m3wrap-v2-acio
AAPL,phandle
interrupts
role
ACIO0
clock-gates
device_type
acio-cpu
name
acio-cpu0
AAPL,phandle
dont-power-on
compatible
iop-nub,rtbuddy-v2
coredump-rel-privacy-approved
watchdog-enable
coredump-enable
no-firmware-service
reconfig-firmware
name
iop-acio0-nub
user-power-managed
power-gates
domain-id
apciec-piodma-sid
clock-gates
#address-cells
AAPL,phandle
msi-vector-offset
pci-aer-correctable
bus-range
acio-parent
name
apciec1
interrupt-parent
port-type
xhci-interrupt-rmbs
dev-range
compatible
apciec,t8103
interrupts
msi-address
apciec-piodma
pci-aer-uncorrectable
ranges
port-number
link-state-power
device_type
pci-c
#size-cells
msi-parent-controller
#msi-vectors
#size-cells
IOPCITunnelLinkChange
PCI-Thunderbolt
AAPL,phandle
#address-cells
AAPL,unit-string
00000000
device-protection-granularity
name
pcic1-bridge
function-dart_force_active
tcaFcompatible
pciec-bridge
function-dart_request_sid
qeRSdefault-apcie-options
pci-ignore-linkstatus
marvel-wa-viddids
msi-for-bridges
AAPL,slot-name
Slot-1
function-dart_release_sid
leRSfunction-dart_self
fleS#msi-vectors
msi-vector-base
protection-granularity
AAPL,phandle
instance
TRADDART
tlb-invalid-refetch
dart-options
sid-count-a0
name
dart-apciec1
sids-a0
interrupt-parent
vm-reserve
compatible
dart,t8020
page-size
interrupts
diag-config
manual-availability
dead-mappings
vm-base
sid-count
relaxed-rw-protections
error-reflector
device_type
dart
sids
AAPL,phandle
name
mapper-apciec1-piodma
compatible
iommu-mapper
device_type
dart-mapper
piodma-max-transfer-size
compatible
pciec-apiodma,t8103
iommu-parent
interrupt-parent
interrupts
piodma-num-address-bits
AAPL,phandle
piodma-max-segment-size
device_type
apciec1-piodma
piodma-byte-alignment
piodma-id
piodma-fifo-size
name
apciec1-piodma
thunderbolt-drom
Apple Inc.
clock-gates
AAPL,phandle
atc-phy-parent
function-dock_parent
Pccaportmap
link-speed-default
gpio-lstx
port-defaults
+revision
iommu-parent
function-pcie_port_control
CtrPT
name
acio1
function-dart_force_active
tcaFinterrupt-parent
link-width-default
port-type
compatible
acio
interrupts
vid-did
 acio-cpu
spec-version
port-number
device_type
acio
power-gates
bypass
compatible
dart,t8020
manual-availability
interrupt-parent
interrupts
page-size
AAPL,phandle
error-reflector
device_type
dart
dart-options
diag-config
instance
TRADDART
sids
tlb-invalid-refetch
name
dart-acio1
AAPL,phandle
name
mapper-acio1
compatible
iommu-mapper
device_type
dart-mapper
interrupt-parent
compatible
iop,m3wrap-v2-acio
AAPL,phandle
interrupts
role
ACIO1
clock-gates
device_type
acio-cpu
name
acio-cpu1
AAPL,phandle
dont-power-on
compatible
iop-nub,rtbuddy-v2
coredump-rel-privacy-approved
watchdog-enable
coredump-enable
no-firmware-service
reconfig-firmware
name
iop-acio1-nub
user-power-managed
ufp-count
name
display-crossbar0
compatible
display-crossbar,t8103
device_type
AAPL,display-crossbar
ufp-endpoints
dispext0
AAPL,phandle
dfp-endpoints
atc0
atc1
AAPL,phandle
name
atc0-dpxbar
compatible
atc-dpxbar,t8103
device_type
AAPL,atc-dpxbar
dp-switch-dfp-endpoint
compatible
atc-dpphy,t8103
transport-tunneled
dp-switch-dfp-port
AAPL,phandle
device_type
AAPL,atc-dpphy
transport-type
atc-phy
port-type
dp-switch-parent
port-number
dpxbar-parent
name
atc0-dpphy
dp-switch-dfp-endpoint
compatible
atc-dpin,t8103
interrupt-parent
interrupts
acio-parent
dp-switch-dfp-port
transport-index
device_type
AAPL,atc-dpin
transport-tunneled
transport-type
atc-phy
port-type
AAPL,phandle
dp-switch-parent
port-number
dpxbar-parent
name
atc0-dpin0
dp-switch-dfp-endpoint
compatible
atc-dpin,t8103
interrupt-parent
interrupts
acio-parent
dp-switch-dfp-port
transport-index
device_type
AAPL,atc-dpin
transport-tunneled
transport-type
atc-phy
port-type
AAPL,phandle
dp-switch-parent
port-number
dpxbar-parent
name
atc0-dpin1
AAPL,phandle
name
atc1-dpxbar
compatible
atc-dpxbar,t8103
device_type
AAPL,atc-dpxbar
dp-switch-dfp-endpoint
compatible
atc-dpphy,t8103
transport-tunneled
dp-switch-dfp-port
AAPL,phandle
device_type
AAPL,atc-dpphy
transport-type
atc-phy
port-type
dp-switch-parent
port-number
dpxbar-parent
name
atc1-dpphy
dp-switch-dfp-endpoint
compatible
atc-dpin,t8103
interrupt-parent
interrupts
acio-parent
dp-switch-dfp-port
transport-index
device_type
AAPL,atc-dpin
transport-tunneled
transport-type
atc-phy
port-type
AAPL,phandle
dp-switch-parent
port-number
dpxbar-parent
name
atc1-dpin0
dp-switch-dfp-endpoint
compatible
atc-dpin,t8103
interrupt-parent
interrupts
acio-parent
dp-switch-dfp-port
transport-index
device_type
AAPL,atc-dpin
transport-tunneled
transport-type
atc-phy
port-type
AAPL,phandle
dp-switch-parent
port-number
dpxbar-parent
name
atc1-dpin1
AAPL,phandle
compatible
mcc,t8103
dcs_num_channels
dramcfg-data
@SUUconfig-data
name
device_type
#interrupt-cells
#main-cpus
compatible
aic,1
interrupt-controller
master
aic-version
#shared-timestamps
AAPL,phandle
device_type
interrupt-controller
#address-cells
ipid-mask
target-destinations
name
name
aic-timebase
AAPL,phandle
device_type
timer
function-panic_halt_helper
!meMwdt-version
compatible
wdt,t8101
wdt,s5l8960x
clock-ids
interrupt-parent
function-panic_notify
OIPG
interrupts
AAPL,phandle
panic-save-flag-bit
device_type
trigger-config
function-panic_flush_helper
@meMname
panic-forcepoweroff-flag-bit
interrupt-parent
compatible
error-handler,t8101
trueuc-irq-enable
interrupts
AAPL,phandle
dcs-num-channels
error-reflector
device_type
error-handler
name
error-handler
dwi-version
lockout-us
compatible
dwi,t8103
dwi,s8000
interrupt-parent
interrupts
clock-gates
AAPL,phandle
str-delay
polarity-config
device_type
nclk-div
name
interrupt-parent
compatible
pwm,t8101
pwm,s5l8920x
AAPL,phandle
interrupts
clock-gates
device_type
name
interrupt-parent
compatible
aes,s8000
interrupts
AAPL,phandle
iommu-parent
clock-gates
device_type
name
aes-version
#interrupt-cells
interrupt-controller
compatible
gpio,t8101
interrupt-parent
interrupts
#gpio-int-groups
#gpio-pins
AAPL,phandle
device_type
interrupt-controller
#address-cells
role
name
gpio
#interrupt-cells
interrupt-controller
compatible
gpio,t8101
interrupt-parent
interrupts
#gpio-int-groups
no-resume-restore
#gpio-pins
device_type
interrupt-controller
supported-int-groups
wake-events
wake-no-interrupt-group
#address-cells
AAPL,phandle
role
name
aop-gpio
#interrupt-cells
interrupt-controller
compatible
gpio,t8101
interrupt-parent
interrupts
#gpio-int-groups
no-resume-restore
#gpio-pins
device_type
interrupt-controller
supported-int-groups
wake-events
wake-no-interrupt-group
#address-cells
AAPL,phandle
role
name
nub-gpio
#interrupt-cells
interrupt-controller
compatible
gpio,t8101
interrupt-parent
interrupts
#gpio-int-groups
no-resume-restore
#gpio-pins
device_type
interrupt-controller
supported-int-groups
wake-events
AAPL,phandle
#address-cells
role
name
smc-gpio
compatible
iop,ascwrap-v4
iommu-parent
interrupt-parent
interrupts
clock-gates
clock-ids
AAPL,phandle
iop-version
device_type
function-pll_off_mode
MLLP
power-gates
role
name
aop-target
sleep-on-hibernate
compatible
iop-nub,rtbuddy-v2
coredump-enable
AAPL,phandle
region-base
firmware-name
mac13gaop
region-size
aop-fr-timebase
enable-doppler
watchdog-enable
aop-memory-alignment
name
iop-aop-nub
AAPL,phandle
name
aop-audio
compatible
aop-audio
device_type
aop-audio
clockSource
 llpchannelsEnabled
AAPL,phandle
pdmcFrequency
fastClockSpeed
channelCount
channelsSupported
pdmFrequency
name
audio-pdm2
micTurnOnTimeMs
bytesPerSample
decimatorConfig
compatible
audio-aop-pdm2
identifier
0mdpchannelPolaritySelect
channelPhaseSelect
voiceTriggerChannel
decimatorConfigForHighPowerClks
micSettleTimeMs
device_type
audio-pdm2
slowClockSpeed
latency
ratios
compatible
audio-aop-pdm2
AAPL,phandle
filterLengths
identifier
80cdname
dc-800000
coefficients
latency
ratios
compatible
audio-aop-pdm2
AAPL,phandle
filterLengths
identifier
42cdname
dc-2400000
coefficients
name
audio-hp
AAPL,phandle
identifier
iaphcompatible
audio-aop-hp
device_type
audio-aop-hp
name
audio-lp-mic-in
AAPL,phandle
identifier
iaplcompatible
audio-aop-lp-mic-in
device_type
audio-aop-lp-mic-in
clock-gates
AAPL,phandle
bypass
instance
FPADDART
tlb-invalid-refetch
dart-options
filter-data-instance-0
name
dart-aop
interrupt-parent
retention
compatible
dart,t8020
interrupts
page-size
diag-config
bypass-address
vm-base
error-reflector
device_type
dart
power-gates
sids
AAPL,phandle
name
mapper-aop
compatible
iommu-mapper
device_type
dart-mapper
device_type
dart-mapper
AAPL,phandle
name
mapper-aop-admac
compatible
iommu-mapper
allow-subpage-mapping
AAPL,phandle
name
mapper-scm
compatible
iommu-mapper
device_type
dart-mapper
bridge-settings-version
ane-acg-hack
ane-dpe
disp-tvm
bridge-counter-configs
MSR PMSG FIFO
MSR M1 RD
MSR M1 WR
MSR LLT
JPEG PMSG FIFO
JPEG M0 RD
JPEG M0 WR
JPEG LLT
AVE PMSG FIFO
AVE M0 RD
AVE M0 WR
AVE LLT
AVD PMSG FIFO
AVD M1 RD
AVD M1 WR
AVD LLT
ANE PMSG FIFO
ANE M0 RD
ANE M0 WR
ANE LLT
ISP PMSG FIFO
ISP M1 RT WR
ISP M0 BULK WR
ISP RT
DISP PMSG FIFO
DISP RT RD
DISP BULKCPU WR
DISP RT
GFX PMSG FIFO
GFX M0 RD
GFX M1 WR
GFX LLT
PCIE PMSG FIFO
PCIE M0 RD
PCIE M1 WR
PCIE LLT
ATC0 PMSG FIFO
ATC0 M0 RD
ATC0 M0 WR
ATC0 LLT
ATC1 PMSG FIFO
ATC1 M0 RD
ATC1 M0 WR
ATC1 LLT
DISPX PMSG FIFO
DISPX M1 RT RD
DISPX M0 BULK WR
DISPX RT
DISPD PMSG FIFO
DISPD M1 RT RD
DISPD M0 BULK WR
DISPD RT
SBR PMSG FIFO
SBR M0 RD
SBR M0 WR
SBR LLT
ANS2 PMSG FIFO
ANS2 M0 RD
ANS2 M0 WR
ANS2 LLT
apsc-snooze
events
SOC_TVM_TEMP_0
SOC_TVM_TEMP_1
SOC_TVM_TEMP_2
SOC_TVM_TEMP_3
GFX_TVM_TEMP_0
GFX_TVM_TEMP_1
GFX_TVM_TEMP_2
GFX_TVM_TEMP_3
DCS_TVM_TEMP_0
DCS_TVM_TEMP_1
DCS_TVM_TEMP_2
DCS_TVM_TEMP_3
DISP_TVM_TEMP_0
DISP_TVM_TEMP_1
DISP_TVM_TEMP_2
DISP_TVM_TEMP_3
SOC_VMIN
SOC_VNOM
SOC_VMAX
DCS_VMIN
DCS_VNOM
DCS_VMAX
DISP_VMIN
DISP_VNOM
DISP_VMAX
ANE_CNT0_ACC1
ANE_CNT1_ACC1
ANE_CNT2_ACC1
ANE_CNT3_ACC1
ANE_CNT4_ACC1
SOCHOT0
SOCHOT1
ECPU_ADCLK_TRIG
PCPU_ADCLK_TRIG
ANE_ADCLK_TRIG
ECPU_DITHR_TRIG
PCPU_DITHR_TRIG
ANE_DITHR_TRIG
GPU_ADCLK_TRIG0
GPU_DITHR_TRIG0
GPU_PMU_T_I_UV
GPU_PRE_UVLO
GPU_SW_SHUTDN
ECPU_PRE_UVLO
ECPU_SW_SHUTDN
PCPU_PMU_T_I_UV
PCPU_PRE_UVLO
PCPU_SW_SHUTDN
ANE_PMU_T_I_UV
ANE_PRE_UVLO
ANE_SW_SHUTDN
SLP_MEDIA
SLP_DDR
AWAKE
SLP_S2R
DEEP_WAIT
ECPU_DPE_THRTL
ECPU0
ECPU1
ECPU2
ECPU3
PCPU_DPE_THRTL
PCPU0
PCPU1
PCPU2
PCPU3
SOC_CSRAM_STS
DCS_CSRAM_STS
GPU_CSRAM_STS
DISP_CSRAM_STS
device-bridges
pwrgate-regs
dvd-period-us
perf-regs
total-rails-leakage
atcusb-poll
gfx-tvm
function-pmp_control
CPMPps-regs
sep-ps-timeout
bridge-reg-index
bridge-counter-version
pmgr-dock-fifo-agent
dvd-factor
ave-tvm
ppt-thrtl
reset-noaccess-poll
clocks
FAST_AF
DISP0
ISP_SENSOR0_REF
ISP_SENSOR1_REF
ISP_SENSOR2_REF
ISP_SENSOR3_REF
VENC
PLL0
PLL1
PLL2
PLL3
PLL4
PLL5
PLL6
PLL7
PLL_GFX
PLL_ANE
PLL_PCIE
LPPLL_FAST
AOP_CLK_SEL_0
AOP_CLK_SEL_1
AOP_CLK_SEL_2
AOP_CLK_SEL_3
AOP_CLK_SEL_4
LPPLL_FAST
cpu-power-gate-latency-us
voltage-states0
name
pmgr
cpu-apsc
dvc-debug
#bridges
axi2af-axi-config
boost-performance1
voltage-states2
dvd-threshold-us
compatible
pmgr1,t8103
gpu-pwc-win-size
AAPL,phandle
mcx-fast-pcpu-frequency
bridge-counters
panic-nub-pg-wa
soc-tvm
frc-cpm-on-hack
rosc-apply
ap-wake-sources
AOP.GPIO.IRQ4
AOP.GPIO.IRQ6
AOP.OutboxNotEmpty
AOP.CPUWakeupAP
NUB.WakeupAP
NUB.GPIO.IRQ6
NUB.APWatchdog
NUB.APWakeupTime
NUB.SPMI0Sw3IRQ
AOP.SPMI0Sw3IRQ
SMC.CPUWakeupAP
SMC.OutboxNotEmpty
ATC0.CIOWakeup
ATC1.CIOWakeup
ATC0.USBWakeup
ATC1.USBWakeup
voltage-states9
power-domains
DCS02
DCS13
DISP0_GP0
DISP0_GP1
DISP0_PPP
PMS_SRAM
APCIE_SYS
SEP_EISP
ANS2
NUB_FABRIC
NUB_SRAM
DEBUG_USB
AOP_CPU
AOP_FILTER
SMC_CPU
SMC_FABRIC
ANE_SYS
DCS46
DCS57
DISP0_CPU
DISP0_BLC
DISPEXT_CPU
DISPEXT_GP0
DISPEXT_GP1
DISPEXT_PPP
ISP_CPU_CORE0
ISP_CPU_CORE1
DISP0_BRC
DEBUG_AUTH
energy-counters
ECPU0
ECPU1
ECPU2
ECPU3
PCPU0
PCPU1
PCPU2
PCPU3
ECPM
PCPM
ECPU
PCPU
CPU Energy
llc-thrtl
devices
ECPU0
ECPU1
ECPU2
ECPU3
PCPU0
PCPU1
ECPM
PCPM
SOC_SPMI0
SOC_SPMI1
SOC_SPMI2
GPIO
PMS_BUSIF
PMS_FPWM0
PMS_FPWM1
PMS_FPWM2
PMS_FPWM3
PMS_FPWM4
SOC_DPE
PMGR_SOC_OCLA
ISPSENS0
ISPSENS1
ISPSENS2
ISPSENS3
PCIE_REF
AFT0
DEVC0_IVDMC
SIO_BUSIF
SIO_CPU
FPWM0
FPWM1
FPWM2
I2C0
I2C1
I2C2
I2C3
I2C4
SPI_P
UART_P
AUDIO_P
SIO_ADMA
SPI0
SPI1
SPI2
SPI3
UART_N
UART0
UART1
UART2
UART3
UART4
UART5
UART6
UART7
UART8
MCA0
MCA1
MCA2
MCA3
MCA4
MCA5
DPA0
DPA1
DCS0
DCS1
DCS2
DCS3
APCIE
DISP0_FE
DISPEXT_FE
DISPEXT_BE
DISPEXT_CPU0
DISPEXT_GP0
DISPEXT_GP1
DISPEXT_PPP
DPEXT
MSR_ASE_CORE
PMS_SRAM
APCIE_GP
ANS2
ISP_SYS
VENC_SYS
AVD_SYS
APCIE_ST
ANE_SYS
SEP_EISP
ISP_CPU_CORE0
ISP_CPU_CORE1
VENC_DMA
VENC_PIPE4
VENC_PIPE5
VENC_ME0
VENC_ME1
ANE_SYS_CPU
DISP0_CPU0
DISP0_BE
DISP0_GP1
DISP0_PPP
DISP0_GP0
DEBUG
NUB_SPMI0
NUB_AON
NUB_GPIO
NUB_FABRIC
NUB_SRAM
DEBUG_USB
DEBUG_AUTH
AOP_FILTER
AOP_GPIO
AOP_BASE
AOP_FR
AOP_SPMI0
AOP_SPMI1
AOP_LEAP_AOPCLK
AOP_SHIM
AOP_ADMA0
AOP_UART0
AOP_UART1
AOP_UART2
AOP_SCM
AOP_CPU
AOP_I2CM0
AOP_I2CM1
AOP_MCA0
AOP_MCA1
AOP_MCA2
AOP_MCA3
AOP_MCA4
AOP_MCA5
AOP_SPI0
AOP_LEAP
AOP_AUDIO_SHIM
AOP_AUDIO_ADMA0
AOP_PDMC_LPD
AOP_SRAM
AOP_PDM0_REF
AOP_PDM1_REF
SMC_FABRIC
SMC_GPIO
SMC_AON
SMC_UART0
SMC_UART1
SMC_I2CM0
SMC_I2CM1
SMC_I2CM2
SMC_FPWM0
SMC_FPWM1
SMC_CPU
CPU-BUSY
ECPU
PCPU
PCPU2
PCPU3
SPI4
DCS4
DCS5
DCS6
DCS7
DISPEXT_DSC
DISPDFR_FE
DISPDFR_BE
ATC0_COMMON
ATC0_PCIE
ATC0_CIO
ATC0_CIO_PCIE
ATC0_CIO_USB
ATC1_COMMON
ATC1_PCIE
ATC1_CIO
ATC1_CIO_PCIE
ATC1_CIO_USB
DISP0_SPMI
DISP0_SPI
DISP0_BRC
DISP0_BLC
MIPI_DSI
NUB_SPMI1
ATC0_USB_AON
ATC1_USB_AON
ATC0_USB
ATC1_USB
SMC_I2CM3
SMC_I2CM4
DISP-MIE-DPB
DISP-FB-ACTIVE
DISP-CARVE-OUT
SIO-CPU-V
C0-USBCTL-V
USB-AUDIO-V
VENC-SYS-V
VENC-FAST
VENC-MEM-FAST
VENC-SOC-VMAX
AVD-SYS-V
AVD-SOC-VNOM
AVD-SOC-VMAX
ANE-SYS-V
JPG0-V
JPG-SOC-VNOM
JPG-SOC-VMAX
MSR-V
ISP-SYS-V
ISP-SOC-VNOM
ISP-SOC-VNAX
SOC-VNOM
SOC-VMAX
DCS-VNOM
DCS-VMAX
GFX-SGX
GFX-BUSY
GFX-ASC
DISPDFR-V
SIO-CPU-DART
ATC0-USB-DART
ATC1-USB-DART
VENC-SYS-DART
AVD-SYS-DART
ANE-SYS-DART
JPG0-DART
MSR-DART
ISP-SYS-DART
VENC-DART
JPG1-DART
JPG1-V
SEP-PEARL-V
FR_SCALING-V
ATC0_RESET-V
ATC1_RESET-V
CIO0_RECONFIG-V
CIO1_RECONFIG-V
ATC0_RESET_C0-V
ATC0_RESET_C1-V
ATC1_RESET_C0-V
ATC1_RESET_C1-V
AUDIO-P-V
ATC0-USB-AUD-V
ATC1-USB-AUD-V
ATC0_PCIE-V
ATC1_PCIE-V
AUSB0_AONUSB-V
AUSB1_AONUSB-V
AUSB0_AONPCIE-V
AUSB1_AONPCIE-V
DFR-MIE-DPB
DFR-FB-ACTIVE
DFR-CARVE-OUT
DISP0_CPU0-V
DISPEXT0_CPU0-V
DP0-SOC-VNAX
DP1-SOC-VNAX
function-perf-boost
DCS_PWR_GATE
DCS_CLK_GATE
IMX_PWR_GATE
CPM_PWR_GATE
DCS_PERF_BOOST
SMX_PWR_GATE
AMCC_CLK_GATE
clusters
aes-domain-hack
nominal-performance1
optional-bridge-mask
amx-thrtl
device_type
pmgr
cpu-fixed-freq-pll-relock
dcs-tvm
perf-domains
ECPU
PCPU
DISP
noise-hack
clpc
pmgr-dock-fifo-channel
soc-dpe
function-mcc_ctrl
$meMP
pkg-avg-limiter-kp
cpu-lowpeak-limiter-input-tc
function-ane_perf_ctr
RCNApkg-avg-limiter-ki
cpu-utilization-target
pkg-power-zone-filter-tc-0
events
cpu-sched-lat-nonui-kp-up
cpu-sched-lat-ui-kp-down
cpu-utilization-kp-down
sampling-interval-ms
pkg-avg-limiter-target-tc
pkg-lowpeak-max-power
cpu-sched-lat-nonui-kp-down
ane-utilization-kp-down
cpu-utilization-ki-down
audio-cpu-target
cpu-perf-cnt-filter-tc-down
cpu-utilization-control-mode
thermal-interval-ms
ane-utilization-ki-down
ui-present-util-target-l1s
cpu-avg-limiter-input-tc
wfe-cluster-residency-threshold
audio-cpu-work-typical-dev
cpu-sched-lat-ui-ki-down
cpu-sched-lat-ui-ki-up
pkg-power-split-cpu-fraction
cpu-sched-lat-ui-target
cpu-sched-lat-nonui-target
pkg-power-split-gpu-fraction
pkg-power-zone-target-offset-0
cpu-power-zone-filter-tc-0
cpu-low-power-frequency-max
pkg-avg-max-power
ui-present-util-target-l0s
cpu-dynamic-control
cpu-sched-lat-nonui-ki-down
pkg-lowpeak-limiter-input-tc
cpu-lowpeak-limiter-target-tc
name
clpc
pkg-peak-power-target-tc
AAPL,phandle
pkg-low-power-target-tc
compatible
clpc,t8103
ca-client-cpu-work-dev-w-down
cpu-perf-cnt-filter-tc-up
cpu-power-zone-target-offset-0
pkg-avg-therm-power-target-tc
pkg-sustainable-power-target
cpu-avg-limiter-target-tc
cpu-utilization-kp-up
pkg-lowpeak-limiter-ki
pkg-lowpeak-limiter-target-tc
cpu-utilization-ki-up
cpu-idle-time-grace-period
ane-utilization-kp-up
pkg-power-zone-target-0
ane-utilization-ki-up
ane-utilization-target
devices
cpu-sched-lat-nonui-ki-up
pkg-lowpeak-limiter-kp
cpu-avg-limiter-kp
cpu-lowpeak-limiter-ki
cpu-power-zone-target-0
cpu-sched-lat-ui-kp-up
audio-cpu-work-window
cpu-core-mask-raise-holdoff
audio-cpu-work-high-floor
device_type
clpc
cpu-avg-limiter-ki
ane-high-td-rate-thresh
cpu-utility-limiter-target
pkg-avg-limiter-input-tc
pkg-lpgm-power-target
pkg-dynamic-split-mode
pkg-avg-batt-power-target-tc
cpu-utility-limiter-enable
cpu-lowpeak-limiter-kp
sbr-clk-gating-wa
AAPL,phandle
cio-config
device-set-9
#device-sets
function-mcc_ctrl
$meMdevice-set-8
device-set-11
device-set-7
devices
device-set-6
device-set-10
gpu-dpe-sync
device-set-5
name
soc-tuner
compatible
soc-tuner,t8020
device-set-4
ane-gpu-mccpwrgt
device-set-3
fr-scaling-wa
device-set-2
mcc-configs
soc-tuning
device-set-1
usb-audio-wa
device-set-0
device_type
soc-tuner
fb-caching
mcc-power-gating
client_budgets
AAPL,phandle
min-update-interval-overrides
cpms-batt2client
package
maintain-pkg-max-insta-handling
cpms-policy-type
client_powers
cpms-dt-topology
droop
spike_power.f
pulse_power.s
name
interrupt-parent
function-btm-stop
PSMBmaintain-1s-min-budget-interval
compatible
ppm,passthrough
interrupts
interrupt-idx-smc-targets
function-btm-config
OCMBbtm-enabled
cpms-dt-curve
package
package
package
reg-idx-smc-targets
clients
AppleCLPC
AppleNAND
AppleH10CamIn
device_type
function-btm-start
TSMBauto-rate-change
device_type
compatible
nco,t8101
nco,s5l8960x
pmgr-nco-page-size
clock-ids
AAPL,phandle
name
interrupt-parent
compatible
event-log-handler,t8101
AAPL,phandle
interrupts
panic-on-event-log-intr
event-logs
device_type
event-log-handler
name
event-log-handler
compatible
iop,ascwrap-v4
iommu-parent
interrupt-parent
interrupts
clock-gates
clock-ids
AAPL,phandle
iop-version
device_type
pio-reg-index
power-gates
pio-vm-base
role
name
pio-vm-size
energy-model-dram-configs
compatible
iop-nub,rtbuddy-v2
firmware-name
t8103pmp
dram-capacity
sram-index
coredump-enable
region-size
region-base
user-power-managed
name
iop-pmp-nub
AAPL,phandle
pio-vm-size
@vm-size
clock-gates
AAPL,phandle
bypass
instance
TRADDART
tlb-invalid-refetch
dart-options
pio-vm-base
name
dart-pmp
interrupt-parent
compatible
dart,t8020
page-size
interrupts
diag-config
bypass-address
pio-granularity
error-reflector
device_type
dart
power-gates
sids
pio-range
AAPL,phandle
name
mapper-pmp
compatible
iommu-mapper
device_type
dart-mapper
compatible
iop-sep,ascwrap-v4
iommu-parent
interrupt-parent
aarch64
interrupts
clock-gates
clock-ids
iop-version
device_type
self-power-gate
sika-support
cpu-ctrl-filtered
AAPL,phandle
power-gates
role
name
rom-panic-bytes
function-wait_for_power_gate
tiaWi
name
iop-sep-nub
compatible
iop-nub,sep
AAPL,phandle
name
Ocelot
AAPL,phandle
name
xART
AAPL,phandle
config
outbox-ctrl-empty-mask
inbox-ctrl-offset
inbox-offset
inbox-ctrl-enable-mask
outbox-offset
outbox-ctrl-offset
reg-block
name
InvalidateHmac
AAPL,phandle
bypass-address
bypass
compatible
dart,t8020
page-size
interrupt-parent
interrupts
AAPL,phandle
error-reflector
device_type
dart
dart-options
diag-config
instance
TRADDART
retention
sids
name
dart-sep
tlb-invalid-refetch
AAPL,phandle
name
mapper-sep
compatible
iommu-mapper
device_type
dart-mapper
map-range
CSIM
compatible
iop,ascwrap-v4
iommu-parent
interrupt-parent
interrupts
clock-gates
clock-ids
device-type
IPSd
RAUd
APDd
iop-version
device_type
AAPL,phandle
dmashim
IPSS
RAUS
DUAS
power-gates
role
name
coredump-enable
no-firmware-service
user-power-managed
name
iop-sio-nub
compatible
iop-nub,rtbuddy-v2
AAPL,phandle
AAPL,phandle
name
sio-dma
compatible
sio-dma-controller
device_type
sio-dma
bypass-address
bypass
compatible
dart,t8020
page-size
interrupt-parent
interrupts
clock-gates
AAPL,phandle
error-reflector
device_type
dart
dart-options
vm-base
diag-config
instance
TRADDART
power-gates
sids
name
dart-sio
tlb-invalid-refetch
device_type
dart-mapper
AAPL,phandle
name
mapper-sio
compatible
iommu-mapper
allow-subpage-mapping
device_type
dart-mapper
AAPL,phandle
name
mapper-aes
compatible
iommu-mapper
allow-subpage-mapping
device_type
dart-mapper
AAPL,phandle
name
mapper-admac
compatible
iommu-mapper
allow-subpage-mapping
nvme-interrupt-idx
iop-version
clock-gates
AAPL,phandle
nvme-linear-sq
iommu-parent
nand-debug
namespaces
interrupt-parent
name
compatible
iop,ascwrap-v4
msp-bfh-params
No_Overrides
interrupts
clock-ids
role
ANS2
tunable-table-bundle
ddlbbjkq
device_type
power-gates
tunable-table-bundle-nand-dev
kgnenjip
continuous-time
compatible
iop-nub,rtbuddy-v2
power-managed
region-base
region-size
AAPL,phandle
no-hibernate-sleep
no-shutdown
crashlog-non-fatal
name
iop-ans-nub
cold-boot-after-hibernate
name
sart-ans
compatible
sart,coastguard
device_type
sart
exclusive-bounds
sart-version
AAPL,phandle
compatible
iop,ascwrap-v4
clock-ids
interrupt-parent
interrupts
clock-gates
AAPL,phandle
iop-version
device_type
role
power-gates
timers-reg-index
name
user-power-managed
quiesced
compatible
iop-nub,rtbuddy-v2
coredump-enable
AAPL,phandle
region-base
firmware-name
t8103smc
no-shutdown
region-size
continuous-time
cold-boot-after-hibernate
no-hibernate-sleep
watchdog-enable
coredump-active-only
name
iop-smc-nub
pre-loaded
#address-cells
event_name-bit12
wifibt
AAPL,phandle
event_name-bit48
event_name-bit44
USB2_wake
has_lid_open_sensor
event_name-bit42
USB-C_plug
event_name-bit8
event_name-bit0
pwrbtn
event_name-bit40
acattach
name
smc-pmu
compatible
smc-pmu
interrupt-controller
event_name-bit9
charger
#interrupt-cells
event_name-bit11
codec
event_name-bit21
trackpadkeyboard
event_name-bit45
aupo
event_name-bit43
USB2_plug
device_type
interrupt-controller
event_name-bit41
acdetach
function-pmu_button
DtuB
device_type
smc-charger-util
compatible
smc-charger-util
dock-num
port-number
port-type
AAPL,phandle
name
smc-charger-util-0
device_type
smc-charger-util
compatible
smc-charger-util
dock-num
port-number
port-type
AAPL,phandle
name
smc-charger-util-1
compatible
uart-1,samsung
clock-ids
interrupt-parent
interrupts
clock-gates
AAPL,phandle
device_type
uart
no-flow-control
function-tx
OIPG
uart-version
boot-console
name
uart0
name
debug-console
AAPL,phandle
interrupt-parent
dock-wstat-mask
compatible
aapl,dock-channels
interrupts
max-aop-clk
AAPL,phandle
enable-sw-drain
device_type
dockchannel
name
dockchannel-uart
compatible
spi-1,spimc
function-spi_cs0
OIPG-
interrupt-parent
interrupts
clock-gates
clock-ids
AAPL,phandle
device_type
#size-cells
#address-cells
spi-version
name
spi1
ranges
compatible
nor-flash,spi
device_type
spinor
AAPL,phandle
#address-cells
#size-cells
name
spinor
AAPL,phandle
name
firmware
compatible
iboot,boot
device_type
firmware
AAPL,phandle
name
anvram
compatible
nvram,chrp
device_type
anvram
AAPL,phandle
name
syscfg
compatible
diagnostic-data,format1
device_type
syscfg
gpio-iic_scl
compatible
i2c,t8101
i2c,s5l8940x
iic,soft
gpio-iic_sda
interrupt-parent
interrupts
clock-gates
clock-ids
#address-cels
device_type
#size-cells
AAPL,phandle
function-device_reset
TSRA'
name
i2c0
AAPL,phandle
interrupt-parent
interrupts
compatible
usbc,manager
name
hpmBusManager
features-supported
transports-supported
port-location
left-back
compatible
usbc,cd3217
acio-parent
iicProvider
usbc-fw-personality-bbr
HPM,29
dock
AAPL,phandle
hpm-class-type
hpm-iic-addr
port-type
usbc-flash-update
usbc-fw-personality
HPM,29_R
port-number
name
hpm0
features-supported
transports-supported
port-location
left-front
compatible
usbc,cd3217
acio-parent
iicProvider
AAPL,phandle
dock
hpm-class-type
hpm-iic-addr
port-type
usbc-flash-update
port-number
name
hpm1
compatible
admac,t8103
iommu-parent
interrupt-parent
interrupts
#dma-channels
clock-gates
channels-offset
clock-ids
device_type
admac
channel-buffer-allocation
irq-destination-index
AAPL,phandle
power-gates
role
 OISirq-destinations
UPCS CIADSNUKLCSname
admac-sio
compatible
admac,t8103
irq-destination-index
interrupt-parent
interrupts
#dma-channels
iommu-parent
channels-offset
clock-ids
device_type
admac
channel-buffer-allocation
external-power
AAPL,phandle
role
 APAirq-destinations
UPCAUPCS CIAKLCSname
admac-aop-audio
compatible
atc-phy,t8103
function-dock_parent
PccaAAPL,phandle
clock-gates
tunable-device
device_type
atc-phy
port-type
instance
port-number
tunable-host
name
atc-phy0
compatible
dart,t8020
page-size
interrupt-parent
interrupts
clock-gates
AAPL,phandle
error-reflector
device_type
dart
remap
dart-options
vm-base
diag-config
instance
TRADDARTLLT
TRADDARTBLK
sids
protection-granularity
name
dart-usb0
tlb-invalid-refetch
name
mapper-usb0
compatible
iommu-mapper
allow-subpage-mapping
dead-mappings
device_type
dart-mapper
AAPL,phandle
tunable_ATC_LINK_REGS
clock-gates
AAPL,phandle
configuration-string
ncmAuxBringup
atc-phy-parent
function-dock_parent
Pccancm-control-ecid-mac
ncm-interrupt-ep-disabled
bus-number
acio-parent
iommu-parent
ncm-self-name-unit
host-mac-address
macaddr/wifiaddr,syscfg/WMac/6,zeroes/6
device-mac-address
macaddr/ethaddr,syscfg/EMac/6,zeroes/6
name
usb-drd0
interrupt-parent
port-type
tunable
tunable_AUSBC_CTLREG
compatible
usb-drd,t8103
interrupts
usb-stream-policy
tunable_setting
usb-tier-limit
usb-port-current-sleep-limit
usb-port-current-wake-limit
port-number
device_type
usb-drd
tunable_AUSBC_BULK_FABRIC
usb-port-number
usb-port-type
AAPL,phandle
name
usb-drd0-port-hs
usb-c-port-number
device_type
usb-drd0-port-hs
usb-port-number
usb-port-type
AAPL,phandle
name
usb-drd0-port-ss
usb-c-port-number
device_type
usb-drd0-port-ss
compatible
atc-phy,t8103
function-dock_parent
PccaAAPL,phandle
clock-gates
tunable-device
device_type
atc-phy
port-type
instance
port-number
tunable-host
name
atc-phy1
compatible
dart,t8020
page-size
interrupt-parent
interrupts
clock-gates
AAPL,phandle
error-reflector
device_type
dart
remap
dart-options
vm-base
diag-config
instance
TRADDARTLLT
TRADDARTBLK
sids
protection-granularity
name
dart-usb1
tlb-invalid-refetch
name
mapper-usb1
compatible
iommu-mapper
allow-subpage-mapping
dead-mappings
device_type
dart-mapper
AAPL,phandle
tunable_ATC_LINK_REGS
clock-gates
AAPL,phandle
configuration-string
ncmAuxBringup
atc-phy-parent
function-dock_parent
Pccancm-control-ecid-mac
ncm-interrupt-ep-disabled
bus-number
acio-parent
iommu-parent
ncm-self-name-unit
host-mac-address
macaddr/wifiaddr,syscfg/WMac/6,zeroes/6
device-mac-address
macaddr/ethaddr,syscfg/EMac/6,zeroes/6
name
usb-drd1
interrupt-parent
port-type
tunable
tunable_AUSBC_CTLREG
compatible
usb-drd,t8103
interrupts
usb-stream-policy
tunable_setting
usb-tier-limit
usb-port-current-sleep-limit
usb-port-current-wake-limit
port-number
usb-restore-disable
device_type
usb-drd
tunable_AUSBC_BULK_FABRIC
usb-port-number
usb-port-type
AAPL,phandle
name
usb-drd1-port-hs
usb-c-port-number
device_type
usb-drd1-port-hs
usb-port-number
usb-port-type
AAPL,phandle
name
usb-drd1-port-ss
usb-c-port-number
device_type
usb-drd1-port-ss
#interrupt-cells
interrupt-controller
compatible
aapl,spmi
fatal-interrupts
interrupt-parent
interrupts
error-interrupts
fault-counters-enabled
device_type
interrupt-controller
AAPL,phandle
queue-depth
#address-cells
name
nub-spmi
other-interrupts
info-fault_shadow
info-has_slpsmc
info-clock_offset
function-external_standby
WyekESBMinfo-rtc_alarm_mask
info-rtc_alarm_offset
info-id
info-fault_name-22
sstate,button_dfu_recover
info-fault_name-15
uv,vdd_boost_uvlo
info-scrpad_socd
ptmu-region-15-data
ptmu-region-1-data
ptmu-region-3-data
info-rtc
info-fault_name-23
crash,scrash_in
info-fault_name-16
spmi,spmi_fault
has-fw
ptmu-region-5-data
info-rtc_alarm_ctrl_en_mask
info-fault_log
info-fault_name-0
info-pm_setting
info-has_phra
ptmu-region-7-data
is-primary
info-fault_name-24
otp_crc
info-fault_name-1
function-suspend_helper
TR2Sinfo-fault_name-17
ntc_shdn
ptmu-region-9-data
upo-shutdown-delay
ptmu-region-10-data
info-fault_name-10
btn_rst,btn_seq_reset
info-fault_name-2
por,por_vddrtc
hw-name
sera
info-fault_name-18
timeout,dblclick_timeout
info-fault_name-25
sgpio
info-fault_name-3
rst,rst_vddrtc
name
spmi-pmu
ptmu-region-11-data
info-fault_name-11
wdog,reset_in_1
AAPL,phandle
info-fault_name-4
ot,overtemp
info-rtc_scrpad
compatible
pmu,spmi
pmu,sera
info-rtc_alarm_event
ptmu-region-0-data
info-fault_name-19
sstate,wallet_crash_seq
info-fault_name-26
ot,temp_abs_buck0
info-fault_name-5
uv,por_warn
info-scrpad
ptmu-region-2-data
info-fault_name-12
dbg_rst,reset_in_2
ptmu-region-12-data
info-fault_name-6
uv,vddmain_uvlo
ptmu-region-4-data
info-leg_scrpad
info-fault_name-27
ot,temp_abs_buck1
info-fault_name-7
ov,vddmain_ovlo
interrupts
ptmu-region-6-data
info-rtc_alarm_ctrl
info-fault_name-13
sochot,reset_in_3
info-fault_name-20
uv,vddmain_uvlo_hold
ptmu-region-13-data
info-fault_name-8
btn_rst,two_finger_rst
interrupt-parent
ptmu-region-8-data
device_type
spmi-pmu
info-fault_name-28
uv,bstlq_uvlo
info-rtc_alarm_monitor_mask
info-fault_name-9
crash,crash_in
info-rtc_irq_mask_offset
info-fault_name-14
btn_shdn
info-fault_name-21
timeout,watchdog_timeout
ptmu-region-14-data
interrupt-parent
function-suspend_helper
TR2Scompatible
interrupts
AAPL,phandle
#num-spmi-interrupts
disable-pmu-filtered-data-read
btm-pmu-type
device_type
name
#interrupt-cells
interrupt-controller
compatible
aapl,spmi
fatal-interrupts
interrupt-parent
interrupts
error-interrupts
fault-counters-enabled
device_type
interrupt-controller
AAPL,phandle
queue-depth
#address-cells
name
nub-spmi1
other-interrupts
ptmu-region-1-data
info-fault_log
info-fault_name-12
uv,internal_boost_uvlo
ptmu-region-13-data
ptmu-region-12-data
ptmu-region-4-data
info-fault_name-5
rst_in,reset_in_rise
ptmu-region-11-data
ptmu-region-7-data
ptmu-region-10-data
interrupt-parent
hw-name
simetra
info-fault_name-6
rst_in,reset_in_fall
interrupts
info-fault_name-13
spmi,spmi_fault
info-fault_name-7
crash,scrash_in
ptmu-region-2-data
ptmu-region-5-data
ptmu-region-8-data
info-fault_name-8
ntc_shdn
has-fw
info-fault_name-9
timeout,watchdog_to
AAPL,phandle
info-scrpad
name
spmi-simetra
info-fault_name-0
info-fault_name-10
otp_crc
ptmu-region-0-data
compatible
pmu,spmi
pmu,simetra
info-fault_name-1
device_type
spmi-simetra
ptmu-region-3-data
ptmu-region-6-data
ptmu-region-9-data
info-fault_name-2
ot,overtemp
info-leg_scrpad
info-fault_name-11
sgpio
function-suspend_helper
TR2Sinfo-fault_name-3
uv,vddmain_uvlo
ptmu-region-15-data
ptmu-region-14-data
info-id
info-fault_name-4
ov,vddmain_ovlo
#interrupt-cells
interrupt-controller
compatible
aapl,spmi
fatal-interrupts
interrupt-parent
interrupts
error-interrupts
fault-counters-enabled
device_type
interrupt-controller
AAPL,phandle
queue-depth
#address-cells
name
aop-spmi1
other-interrupts
interrupt-parent
compatible
nfc,primary,spmi
AAPL,phandle
interrupts
#num-spmi-interrupts
required-functions
support_host_wake_spmi
support_data_over_spmi
name
stockholm-spmi
device_type
stockholm-spmi
AAPL,phandle
required-gpios
support_venable
support_virtual_gpio
function-enable
4WKpf0Pg
name
stockholm
compatible
nfc,primary,gpio
device_type
stockholm
AAPL,phandle
function-saca_cdi0
ACAS0idcfunction-saca_edp0
ACAS0pdename
lcd0-sac
device_type
lpdp-sac
clock-gates
dot-pitch
AAPL,phandle
display-default-color
function-bw_req_interrupt0
QRIBT
temperature-compensation
display-timing-info
function-pmu_ram_access
UmarLACD
max-scaling-ratio
iommu-parent
function-mcc_dataset
SD$Mname
disp0
interrupt-parent
function-pcc_update
UCCPcompatible
disp0,t8103
clock-ids
interrupts
device_type
display-subsystem
KSF_version
power-gates
vm-size
AAPL,phandle
bypass
instance
TRADDART
UMMSSMMU
tlb-invalid-refetch
dart-options
name
dart-disp0
interrupt-parent
compatible
dart,t8020
interrupts
page-size
diag-config
never-lock
real-time
bypass-address
ignore-inconsistent-hw-locks
vm-base
smmu-clock-gating
error-reflector
device_type
dart
allow-pte-remap
sids
device_type
dart-mapper
compatible
iommu-mapper
iomd-cache-ttl
iomd-cache-size
AAPL,phandle
name
mapper-disp0
device_type
dart-mapper
compatible
iommu-mapper
iomd-cache-ttl
iomd-cache-size
AAPL,phandle
name
mapper-disp0-piodma
role
AAPL,phandle
join-power-plane
name
dcp0-expert
compatible
dcp-expert-v1
device_type
dcp-expert
compatible
iop,ascwrap-v4
hdcp-channels
interrupt-parent
interrupts
iommu-parent
clock-gates
clock-ids
audio
iop-version
device_type
AAPL,phandle
truncate-dvas
power-gates
hdcp-parent
role
name
AAPL,phandle
quiesced
compatible
iop-nub,rtbuddy-v2
coredump-rel-privacy-approved
watchdog-enable
coredump-enable
no-firmware-service
user-power-managed
name
iop-dcp-nub
cold-boot-after-hibernate
sids
AAPL,phandle
bypass
instance
TRADDARTASC
remap
dart-options
tlb-invalid-refetch
name
dart-dcp
interrupt-parent
compatible
dart,t8020
page-size
interrupts
diag-config
bypass-address
vm-base
smmu-clock-gating
error-reflector
device_type
dart
vm-size
device_type
dart-mapper
compatible
iommu-mapper
iomd-cache-ttl
iomd-cache-size
AAPL,phandle
name
mapper-dcp
AAPL,phandle
sac-index-count
function-saca_lcd0
ACAS0dclfunction-saca_lcd1
ACAS1dclname
dcp-sac-controller
device_type
dcp-sac-controller
dma-channels
power-gates
dma-parent
clock-gates
function-device_reset_dpa
TSRAE
device_type
dp-audio0
name
dp-audio0
AAPL,phandle
compatible
dispext0,t8103
framebuffer-width
clock-ids
external
clock-gates
function-bw_req_interrupt0
QRIBU
framebuffer-height
display-default-color
device_type
ext-display-subsystem
clcdclk_frequency
CBGR
display-timing-info
interrupt-parent
interrupts
iommu-parent
dot-pitch
power-gates
AAPL,phandle
name
dispext0
sids
vm-size
clock-gates
AAPL,phandle
bypass
instance
TRADDART
UMMSSMMU
tlb-invalid-refetch
dart-options
name
dart-dispext0
interrupt-parent
compatible
dart,t8020
interrupts
page-size
diag-config
never-lock
real-time
bypass-address
ignore-inconsistent-hw-locks
vm-base
smmu-clock-gating
error-reflector
device_type
dart
allow-pte-remap
power-gates
device_type
dart-mapper
compatible
iommu-mapper
iomd-cache-ttl
iomd-cache-size
AAPL,phandle
name
mapper-dispext0
device_type
dart-mapper
compatible
iommu-mapper
iomd-cache-ttl
iomd-cache-size
AAPL,phandle
name
mapper-dispext0-piodma
role
DCPEXT
AAPL,phandle
join-power-plane
name
dcpext-expert
compatible
dcp-expert-v1
device_type
dcp-expert
hdcp-channels
iop-version
clock-gates
AAPL,phandle
dp-switch-parent
iommu-parent
name
dcpext
interrupt-parent
compatible
iop,ascwrap-v4
clock-ids
interrupts
hdcp-parent
audio
dp-switch-ufp-endpoint
dp-switch-ufp-port
dcp-soc-vmax-index
role
DCPEXT
truncate-dvas
device_type
power-gates
AAPL,phandle
compatible
iop-nub,rtbuddy-v2
quiesced
coredump-rel-privacy-approved
watchdog-enable
coredump-enable
external-index
no-firmware-service
user-power-managed
name
iop-dcpext-nub
cold-boot-after-hibernate
sids
AAPL,phandle
bypass
instance
TRADDARTASC
remap
dart-options
tlb-invalid-refetch
name
dart-dcpext
interrupt-parent
compatible
dart,t8020
page-size
interrupts
diag-config
bypass-address
vm-base
smmu-clock-gating
error-reflector
device_type
dart
vm-size
device_type
dart-mapper
compatible
iommu-mapper
iomd-cache-ttl
iomd-cache-size
AAPL,phandle
name
mapper-dcpext
dma-channels
power-gates
dma-parent
clock-gates
function-device_reset_dpa
TSRAF
device_type
dp-audio1
name
dp-audio1
AAPL,phandle
hardware-version
compatible
scaler,t8103
scaler,s5l8960x
iommu-parent
interrupt-parent
interrupts
clock-gates
clock-ids
workload-priority
device_type
scaler
AAPL,phandle
function-device_reset
TSRA]
power-gates
coprovider-group
scaler
name
scaler0
manual-availability
compatible
dart,t8020
page-size
interrupt-parent
interrupts
clock-gates
AAPL,phandle
error-reflector
device_type
dart
dart-options
diag-config
instance
TRADDART
power-gates
sids
name
dart-scaler
tlb-invalid-refetch
device_type
dart-mapper
compatible
iommu-mapper
iomd-cache-ttl
iomd-cache-size
AAPL,phandle
name
mapper-scaler
device_type
dart-mapper
compatible
iommu-mapper
iomd-cache-ttl
iomd-cache-size
AAPL,phandle
name
mapper-scaler-piodma
compatible
jpeg,t8101
jpeg,s5l8920x
iommu-parent
interrupt-parent
interrupts
hw-type
a0103
clock-gates
clock-ids
device_type
jpeg
AAPL,phandle
power-gates
coprovider-group
jpeg
name
jpeg0
compatible
dart,t8020
page-size
interrupt-parent
interrupts
clock-gates
AAPL,phandle
error-reflector
device_type
dart
dart-options
diag-config
power-gates
instance
TRADDART
sids
name
dart-jpeg0
tlb-invalid-refetch
dead-mappings
AAPL,phandle
name
mapper-jpeg0
compatible
iommu-mapper
device_type
dart-mapper
compatible
jpeg,t8101
jpeg,s5l8920x
iommu-parent
interrupt-parent
interrupts
hw-type
a0103
clock-gates
clock-ids
device_type
jpeg
AAPL,phandle
power-gates
coprovider-group
jpeg
name
jpeg1
compatible
dart,t8020
page-size
interrupt-parent
interrupts
clock-gates
AAPL,phandle
error-reflector
device_type
dart
dart-options
diag-config
power-gates
instance
TRADDART
sids
name
dart-jpeg1
tlb-invalid-refetch
dead-mappings
AAPL,phandle
name
mapper-jpeg1
compatible
iommu-mapper
device_type
dart-mapper
compatible
ave2
iommu-parent
interrupt-parent
interrupts
clock-gates
clock-ids
AAPL,phandle
device_type
function-clock_req_interrupt
QRICe
function-mcc_dataset
SD$Mpower-gates
soc-id
t8103
name
clock-gates
AAPL,phandle
bypass
instance
FPADCPUDART
TRADDART
UMMSSMMU
tlb-invalid-refetch
remap
dart-options
filter-data-instance-0
name
dart-ave
interrupt-parent
compatible
dart,t8020
interrupts
page-size
diag-config
bypass-address
vm-base
smmu-clock-gating
error-reflector
device_type
dart
power-gates
sids
device_type
dart-mapper
compatible
iommu-mapper
iomd-cache-ttl
iomd-cache-size
AAPL,phandle
name
mapper-ave
compatible
avd,t8103
clock-ids
interrupt-parent
interrupts
clock-gates
iommu-parent
AAPL,phandle
device_type
avd-version
function-avd_reset
TSRAf
h264-playback-level
ads-present
decode-samples-per-second
function-mcc_dataset
SD$Mpower-gates
name
bypass-address
bypass
compatible
dart,t8020
page-size
interrupt-parent
interrupts
clock-gates
AAPL,phandle
error-reflector
device_type
dart
dart-options
diag-config
instance
TRADDART
power-gates
sids
name
dart-avd
tlb-invalid-refetch
device_type
dart-mapper
compatible
iommu-mapper
iomd-cache-ttl
iomd-cache-size
AAPL,phandle
name
mapper-avd
device_type
dart-mapper
compatible
iommu-mapper
iomd-cache-ttl
iomd-cache-size
AAPL,phandle
name
mapper-avd-piodma
device_type
dart-mapper
compatible
iommu-mapper
iomd-cache-ttl
iomd-cache-size
AAPL,phandle
name
mapper-avd-adsbuf
function-bw_req_interrupt
QRIBd
function-ane_data_param_get
teGdfunction-ane_ep_control
ltCefunction-saca0c
ACAS4macinterrupt-parent
interrupts
has-sphere
function-saca2c
ACAS5macfunction-saca3b
ACASCmacfunction-saca5
ACASGmaciommu-parent
face-detection-support
function-ane_data_param_set
teSdcamera-rear
function-saca4c
ACASFmacclock-gates
multi-camera-cal-wide
syscfg/WDDC
function-saca0
ACAS0macrosaline-calibration-current
syscfg/RxCL
function-saca4
ACASDmacAAPL,phandle
name
power-gates
function-clock_req_interrupt
QRICd
function-saca0b
ACAS2macclock-ids
multi-camera-cal-swide
syscfg/SWDC
device_type
compatible
isp,t8103
isp,s5l8960x
function-saca2b
ACAS3macfunction-saca3
ACASBmacfunction-device_reset
TSRAd
camera-front
function-mcc_dataset
SD$Mfunction-saca2d
ACAS6macpearl-hw-metrics
syscfg/PTPM
function-saca4b
ACASEmacpearl-calibration-data
syscfg/PrCL
back-camera-autofocus-recal
syscfg/BCAR
sensor-type
function-saca2
ACAS1maccamera-strobe-color-cal
syscfg/STRB
sids
vm-size
clock-gates
AAPL,phandle
bypass
instance
FPADDARTLLT
TRADDARTBLK
TRADDARTRT
UMMSSMMUBLK
UMMSSMMURT
tlb-invalid-refetch
dart-options
pio-vm-base
filter-data-instance-0
c@p;
name
dart-isp
interrupt-parent
compatible
dart,t8020
interrupts
page-size
diag-config
manual-availability
real-time
bypass-address
vm-base
smmu-clock-gating
pio-granularity
error-reflector
device_type
dart
power-gates
pio-vm-size
AAPL,phandle
compatible
iommu-mapper
pio-range
iomd-cache-ttl
iomd-cache-size
name
mapper-isp
device_type
dart-mapper
ane-type
compatible
ane,t8020
iommu-parent
interrupt-parent
interrupts
clock-gates
clock-ids
AAPL,phandle
device_type
power-gates
function-mcc_dataset
SD$Mname
sids
vm-size
clock-gates
AAPL,phandle
bypass
instance
FPADDARTLLT
TRADDARTBRD
TRADDARTBWR
tlb-invalid-refetch
dart-options
pio-vm-base
filter-data-instance-0
name
dart-ane
interrupt-parent
compatible
dart,t8020
interrupts
page-size
diag-config
manual-availability
bypass-address
vm-base
pio-granularity
error-reflector
device_type
dart
power-gates
pio-vm-size
AAPL,phandle
compatible
iommu-mapper
pio-range
iomd-cache-ttl
iomd-cache-size
name
mapper-ane
device_type
dart-mapper
gpu-ppm-filter-time-constant-ms
gpu-avg-power-ki-only
@gpu-avg-power-target-filter-tc
procedural-antialiasing
gpu-perf-proportional-gain2
@gpu-perf-filter-drop-threshold
opengl-standard
clock-ids
gpu-fast-die0-integral-gain
HCgpu-fast-die0-proportional-gain
@has-kf
gpu-sochot-temp
gpu-perf-filter-time-constant
gpu-pwr-integral-gain
<metal-standard
gpu-pwr-integral-min-clamp
product-dram
gpu-ppm-ki
Bmeta-sw-interrupt
perf-states
name
AAPL,phandle
gfx-qos
gpu-fast-die0-target
compatible
gpu,t8103
clock-gates
gpu-pwr-perf-scale0
gpu-avg-power-kp
@function-mcc_dataset
SD$Mgpu-perf-integral-gain2
!!J>gpu-power-sample-period
gpu-perf-base-pstate
gpu-avg-power-filter-tc-ms
gpu-power-zone-target-0
gpu-pwr-proportional-gain
@gpu-power-zone-target-offset-0
perf-state-count
interrupts
gpu-perf-filter-time-constant2
gpu-perf-integral-min-clamp
gpu-pwr-min-duty-cycle
interrupt-parent
gpu-num-perf-states
gpu-power-zone-filter-tc-0
device_type
gpu-fast-die0-alarm-threshold
gpu-pwr-perf-scale1
?gpu-pwr-filter-time-constant
gpu-fast-die0-sensor-mask
gpu-avg-power-min-duty-cycle
gpu-ppm-kp
@gpu-perf-tgt-utilization
compatible
iop,ascwrap-v4
iommu-parent
interrupt-parent
interrupts
clock-gates
clock-ids
AAPL,phandle
iop-version
device_type
gfx-asc
role
power-gates
name
gfx-asc
shutdown-sleep
compatible
iop-nub,rtbuddy-v2
firmware-name
coredump-rel-privacy-approved
AAPL,phandle
coredump-enable
no-firmware-service
name
iop-gfx-nub
power-managed
true
compatible
iommu-mapper,gfx
name
mapper-gfx-asc
AAPL,phandle
name
mca-switch
compatible
mca-switch,t8103
numClusters
regStride
AAPL,phandle
clock-gates
device_type
mca-switch
mca-identity
.0wsreg
mca-identity
.0lccompatible
mcaCluster,t8103
sio_mca-version
interrupt-parent
interrupts
AAPL,phandle
device_type
mca_dma-version
#size-cells
function-switch_config
Cacm.0lc#address-cells
name
mca0
mclk-config
mca-identity
a0cmcompatible
mca,t8103
AAPL,phandle
function-i2s_route
Rs2i0.xt0nip
dma-channels
function-admac_powerswitch
CSPasyncGen-config
0nys0gkc0nys0gkcdevice_type
dma-parent
external-power-provider
function-i2s_route0
Rs2i0.xr0.xt
name
mca0a
AAPL,phandle
name
audio-loopback
compatible
audio-data,audio-loopback
data-sources
c2pa
Loopback
device_type
audio-data
private
mca-identity
.1lccompatible
mcaCluster,t8103
sio_mca-version
interrupt-parent
interrupts
AAPL,phandle
device_type
mca_dma-version
#size-cells
function-switch_config
Cacm.1lc#address-cells
name
mca1
mclk-config
mca-identity
a1cmcompatible
mca,t8103
function-mclk_frequency
fOCN
AAPL,phandle
function-i2s_route
Rs2i2dua0nip
dma-channels
function-admac_powerswitch
CSPadevice_type
dma-parent
external-power-provider
function-i2s_route0
Rs2i2dua1nip
name
mca1a
AAPL,phandle
name
audio-tas5770L-spkr-l
compatible
audio-data,tas5770
device_type
audio-data
mca-identity
.3lccompatible
mcaCluster,t8103
sio_mca-version
interrupt-parent
interrupts
AAPL,phandle
device_type
mca_dma-version
#size-cells
function-switch_config
Cacm.3lc#address-cells
name
mca3
mclk-config
mca-identity
a3cmcompatible
mca,t8103
function-mclk_frequency
fOCN
AAPL,phandle
function-i2s_route
Rs2i6dua2nip
dma-channels
function-admac_powerswitch
CSPadevice_type
dma-parent
external-power-provider
name
mca3a
AAPL,phandle
name
audio-codec-output
compatible
audio-data,cs42l83
device_type
audio-data
mclk-config
mca-identity
b3cmcompatible
mca,t8103
function-mclk_frequency
fOCN
AAPL,phandle
function-i2s_route
Rs2i7dua2nip
dma-channels
function-admac_powerswitch
CSPainternal-bclk-loopback
device_type
dma-parent
external-power-provider
name
mca3b
AAPL,phandle
name
audio-codec-input
compatible
audio-data,cs42l83-input
data-sources
c2pa
codecinput
device_type
audio-data
private
disable-aets-fastdie
sensor-offset-CTL2
sensor-offset-CTL4-clr
location-id
i0hTAAPL,phandle
alarm1-temp
sensor-AvgMax
location-name
PMGR SOC Die Temp Sensor0
alarm0-temp
sensor-offset-CTL1
sensor-offset-status
sensor-offset-readSum
sensor-offset-CTL3-set
reg-64-bit
sensor-accuracy
sensor-offset-CTL3-clr
name
tempsensor0
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL4-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
sensor-offset-CTL2
sensor-offset-CTL4-clr
location-id
i1hTAAPL,phandle
alarm1-temp
sensor-AvgMax
location-name
PMGR SOC Die Temp Sensor1
alarm0-temp
sensor-offset-CTL1
sensor-offset-status
sensor-offset-readSum
sensor-offset-CTL3-set
reg-64-bit
sensor-accuracy
sensor-offset-CTL3-clr
name
tempsensor1
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL4-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
sensor-offset-CTL2
sensor-offset-CTL4-clr
location-id
i2hTAAPL,phandle
alarm1-temp
sensor-AvgMax
location-name
PMGR SOC Die Temp Sensor2
alarm0-temp
sensor-offset-CTL1
sensor-offset-status
sensor-offset-readSum
sensor-offset-CTL3-set
reg-64-bit
sensor-accuracy
sensor-offset-CTL3-clr
name
tempsensor2
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL4-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
location-id
i0sTsensor-offset-CTL2-clr
AAPL,phandle
alarm1-temp
invokes-sochot
location-name
SOC MTR Temp Sensor0
alarm0-temp
sensor-AvgMax
sensor-offset-status
sensor-offset-readSum
reg-64-bit
sensor-accuracy
sensor-offset-CTL1-clr
sensor-offset-CTL1-set
name
mtrtempsensor3
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL2-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
location-id
i1sTsensor-offset-CTL2-clr
AAPL,phandle
alarm1-temp
invokes-sochot
location-name
SOC MTR Temp Sensor1
alarm0-temp
sensor-AvgMax
sensor-offset-status
sensor-offset-readSum
reg-64-bit
sensor-accuracy
sensor-offset-CTL1-clr
sensor-offset-CTL1-set
name
mtrtempsensor4
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL2-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
location-id
i2sTsensor-offset-CTL2-clr
AAPL,phandle
alarm1-temp
invokes-sochot
location-name
SOC MTR Temp Sensor2
alarm0-temp
sensor-AvgMax
sensor-offset-status
sensor-offset-readSum
reg-64-bit
sensor-accuracy
sensor-offset-CTL1-clr
sensor-offset-CTL1-set
name
mtrtempsensor5
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL2-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
location-id
i1aTsensor-offset-CTL2-clr
AAPL,phandle
alarm1-temp
invokes-sochot
location-name
ANE MTR Temp Sensor1
alarm0-temp
sensor-AvgMax
sensor-offset-status
sensor-offset-readSum
reg-64-bit
sensor-accuracy
sensor-offset-CTL1-clr
sensor-offset-CTL1-set
name
mtrtempsensor6
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL2-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
location-id
i5sTsensor-offset-CTL2-clr
AAPL,phandle
alarm1-temp
invokes-sochot
location-name
ISP MTR Temp Sensor5
alarm0-temp
sensor-AvgMax
sensor-offset-status
sensor-offset-readSum
reg-64-bit
sensor-accuracy
sensor-offset-CTL1-clr
sensor-offset-CTL1-set
name
mtrtempsensor7
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL2-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
location-id
i0eTsensor-offset-CTL2-clr
AAPL,phandle
alarm1-temp
invokes-sochot
location-name
eACC MTR Temp Sensor0
alarm0-temp
sensor-AvgMax
sensor-offset-status
sensor-offset-readSum
reg-64-bit
sensor-accuracy
sensor-offset-CTL1-clr
sensor-offset-CTL1-set
name
mtrtempsensor8
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL2-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
location-id
i3eTsensor-offset-CTL2-clr
AAPL,phandle
alarm1-temp
invokes-sochot
location-name
eACC MTR Temp Sensor3
alarm0-temp
sensor-AvgMax
sensor-offset-status
sensor-offset-readSum
reg-64-bit
sensor-accuracy
sensor-offset-CTL1-clr
sensor-offset-CTL1-set
name
mtrtempsensor9
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL2-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
location-id
i2pTsensor-offset-CTL2-clr
AAPL,phandle
alarm1-temp
invokes-sochot
location-name
pACC MTR Temp Sensor2
alarm0-temp
sensor-AvgMax
sensor-offset-status
sensor-offset-readSum
reg-64-bit
sensor-accuracy
sensor-offset-CTL1-clr
sensor-offset-CTL1-set
name
mtrtempsensor10
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL2-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
location-id
i3pTsensor-offset-CTL2-clr
AAPL,phandle
alarm1-temp
invokes-sochot
location-name
pACC MTR Temp Sensor3
alarm0-temp
sensor-AvgMax
sensor-offset-status
sensor-offset-readSum
reg-64-bit
sensor-accuracy
sensor-offset-CTL1-clr
sensor-offset-CTL1-set
name
mtrtempsensor11
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL2-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
location-id
i4pTsensor-offset-CTL2-clr
AAPL,phandle
alarm1-temp
invokes-sochot
location-name
pACC MTR Temp Sensor4
alarm0-temp
sensor-AvgMax
sensor-offset-status
sensor-offset-readSum
reg-64-bit
sensor-accuracy
sensor-offset-CTL1-clr
sensor-offset-CTL1-set
name
mtrtempsensor12
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL2-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
location-id
i5pTsensor-offset-CTL2-clr
AAPL,phandle
alarm1-temp
invokes-sochot
location-name
pACC MTR Temp Sensor5
alarm0-temp
sensor-AvgMax
sensor-offset-status
sensor-offset-readSum
reg-64-bit
sensor-accuracy
sensor-offset-CTL1-clr
sensor-offset-CTL1-set
name
mtrtempsensor13
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL2-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
location-id
i7pTsensor-offset-CTL2-clr
AAPL,phandle
alarm1-temp
invokes-sochot
location-name
pACC MTR Temp Sensor7
alarm0-temp
sensor-AvgMax
sensor-offset-status
sensor-offset-readSum
reg-64-bit
sensor-accuracy
sensor-offset-CTL1-clr
sensor-offset-CTL1-set
name
mtrtempsensor14
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL2-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
location-id
i8pTsensor-offset-CTL2-clr
AAPL,phandle
alarm1-temp
invokes-sochot
location-name
pACC MTR Temp Sensor8
alarm0-temp
sensor-AvgMax
sensor-offset-status
sensor-offset-readSum
reg-64-bit
sensor-accuracy
sensor-offset-CTL1-clr
sensor-offset-CTL1-set
name
mtrtempsensor15
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL2-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
disable-aets-fastdie
location-id
i9pTsensor-offset-CTL2-clr
AAPL,phandle
alarm1-temp
invokes-sochot
location-name
pACC MTR Temp Sensor9
alarm0-temp
sensor-AvgMax
sensor-offset-status
sensor-offset-readSum
reg-64-bit
sensor-accuracy
sensor-offset-CTL1-clr
sensor-offset-CTL1-set
name
mtrtempsensor16
sensor-offset-readBK
sensor-offset-alarm0
sensor-offset-readCnt
compatible
tempsensor,t8020
sensor-offset-alarm1
sensor-offset-alarm2
sensor-offset-readBK1
sensor-offset-alarm3
sensor-offset-CTL2-set
device_type
tempsensor
sensor-offset-CTL0-set
sensor-offset-CTL0-clr
sensor-accuracy
compatible
mtrtempsensor,t8020
reg-64-bit
AAPL,phandle
sensor-offset-readBK1
sensor-offset-readBK
device_type
tempsensor
location-name
GPU MTR Temp Sensor1
sensor-AvgMax
location-id
i1gTname
mtrtempsensor17
sensor-accuracy
compatible
mtrtempsensor,t8020
reg-64-bit
AAPL,phandle
sensor-offset-readBK1
sensor-offset-readBK
device_type
tempsensor
location-name
GPU MTR Temp Sensor4
sensor-AvgMax
location-id
i4gTname
mtrtempsensor18
sensor
mTPLTe3zTs5zTa1zTp2zTp3zTp4zTp5zTp7zTp8zTp9ztGAMcompatible
smc-tempsensor
gpu-temperature-offset-value
timerInterval
AAPL,phandle
readGPUDieTemp
gpu-maxTemperature-offset-value
name
smctempsensor0
device_type
smctempsensor
aft-control-list
AAPL,phandle
aft-instance-list
enable_trace
trace_halt
enable_stop_clocks
device_type
cpu-debug-interface
aft-params
panic-trace-mode
cpu_halt
stop_clocks
name
cpu-debug-interface
enable_alt_trace
AAPL,phandle
name
compatible
aft,t8101
device_type
AAPL,phandle
function-perf_cycle_count
TNCP
frequency-stability-upper
local-clock
frequency-tolerance-lower
oscillator-type
frequency-stability-lower
frequency-tolerance-upper
name
timesync
AAPL,phandle
name
apple-processor-trace
compatible
apple-processor-trace,t8103
device_type
apple-processor-trace
compatible
buttons
press-count-tracking
press-count-double-timeout
HIDServiceGlobalModifiersUsage
AAPL,phandle
device_type
buttons
button-names
hold
panic-usage
platform-type-consumer
press-count-triple-timeout
function-button_hold
RntbDLHbpress-count-usage-pairs
name
buttons
device_type
port-component-usb-c
compatible
dock,usb-c
AAPL,phandle
primary-port-id
port-number
port-type
transports-supported
name
port-usb-c-1
device_type
port-component-usb-c
compatible
dock,usb-c
AAPL,phandle
primary-port-id
port-number
port-type
transports-supported
name
port-usb-c-2
mA2Nits1stOrderCoef
mA2Nits2ndOrderCoef
min-restriction-disableth
nits2mAmps1stOrderCoef
pre-strobe-dim-period
truetone-shift-a
LmaxProduct
LmidProduct
truetone-shift-b
blr-cct-warning
calibratedMidCurrent
max-restriction-disableth2
nits2mAmps2ndOrderCoef
min-restriction-enableth
calibratedMaxCurrent
use-trinity
LminProduct
nits2mAmps0thOrderCoef
max-restriction-disableth
mA2Nits0thOrderCoef
AAPL,phandle
milliAmps2DACPart1MaxCurrent
backlight-marketing-table
milliAmps2DACPart2MaxCurrent
milliAmps2DACTablePart1
name
backlight
backlight-update-policy
iDAC2MilliAmpsTable
max-restriction-factor
device_type
backlight
use-AAB-architecture
min-restriction-factor
max-restriction-enableth
max-restriction-factor-aaboff
use-cabal
sync-backlight-off
sync-wake-ramp
min-restriction-factor-aaboff
default-whitepoint-type
dcp-brightness-node
energy-saving
milliAmps2DACTablePart2
compatible
sacm,1
name
sacm
AAPL,phandle
kern.io_throttle_period_tier3
AAPL,phandle
pmap-io-filters
UCMD
UCMD
UCMD
UCMD
UCMD
UCMD
UCMD
UCMD
UCMD
UCMD
UCMD 
UCMD
UCMD
UCMD
UCMD
UCMD
UCMDL
UCMD
UCMD
UCMD
UCMD
UCMD
UCMD
no-effaceable-storage
nvme-iboot-sptm-security
l2-ecc-correctable-panic
content-protect
ean-storage-present
pmap-max-asids
kern.thread_group_extra_bytes
cpx-encryption-mode
name
defaults
trm-profile
kern.io_throttle_window_tier3
data-journaling
aes-service-publish-timeout
serial-device
dual-spi-nand
usb-storage-workaround
trm-enabled
entangle-nonce
pmap-io-ranges
eICP
eICP
eICP
eICP
eICP
eICP
eICP
TRAD
TRAD
TRAD
FPAD
TRAD
FPAD
TRAD
TRAD
TRAD
TRAD
TRAD
TRAD
FPAD
UMMS
TRAD
TRAD
FPAD
TRAD
TRAD
TRAD
TRAD
TRAD
TRAD
FPAD
UMMS
TRAD
UMMS
TRAD
TRAD
UMMS
TRAD
FPAD
FPAD
TRAD
UMMS
TRAD
TRAD
UMMS
DISD
DISD
DISD
eMVN
eMVN
eMVN
2TRS
TRAD
DISD
DISD
TRAD
TRAD
CAMH
FPAD
TRAD
FPAD
FPAD
TRAD
UMMS
YPCS
UCMD
LLPD
ACMD
QDMD
UCMD
LLPD
ACMD
QDMD
UCMD
LLPD
ACMD
QDMD
UCMD
LLPD
ACMD
QDMD
UCMD
LLPD
ACMD
QDMD
UCMD
LLPD
ACMD
QDMD
UCMD
LLPD
ACMD
QDMD
UCMD
LLPD
ACMD
QDMD
YNED
YNED
RBVR
RBVR
RBVR
RBVR
RBVR
RBVR
RBVR
RBVRkern.vm_compressor
panic-reset-type
has-virtualization
external-hdr
graphics-featureset-fallbacks
APPLE6:APPLE5:APPLE4:APPLE3:APPLE3v1:APPLE2:APPLE1:GLES2,0
device-perf-memory-class
has-applelpm
fdr-product-type
MacBookAir10,1
display-mirroring
artwork-device-subtype
builtin-mics
compatible-device-fallback
iPad8,6
app-macho-architecture
arm64
allow-32bit-apps
chrome-identifier
panel-serial-number
syscfg/LCM#
display-backlight-compensation
syscfg/DBCl
has-boot-chime
product-description
MacBook Air (M1, 2020)
framebuffer-identifier
product-soc-name
Apple M1
sub-product-type
MacBookAir10,1
usb-c-smc-pwr
sandman-support
atomic-firmware-update-support
upgradeable-memory
RF-exposure-separation-distance
single-stage-boot
unique-model
J313AP
AAPL,phandle
name
product
graphics-featureset-class
APPLE7
wifi-chipset
4378
mobiledevice-min-ver
1810
os-min-ver
20.0.0.0.0,0
compatible-app-variant
MacFamily20,1
display-temp-compensation
syscfg/DTCl
exclaves-enabled
ptp-large-files
artwork-dynamic-displaymode
dual-iboot-support
has-exclaves
partition-style
macOS
product-name
MacBook Air (M1, 2020)
product-id
bluetooth-le
udid-version
ephemeral-data-mode
lockdown-certtype
primary-calibration-matrix
public-key-accelerator
artwork-scale-factor
artwork-device-idiom
artwork-display-gamut
aggregate-cam-video-zoom
rear-max-video-fps-4k
portrait-lighting-strength
rear-max-video-zoom
studio-light-portrait-preview
rear-max-burst-length
jasper-camera
camera-hdr-version
front-max-burst-length
auto-focus
front-auto-hdr
rear-cam-superwide-capability
front-burst-image-duration
rear-max-video-fps-720p
flash
pipelined-stillimage-capability
front-hdr
rear-max-slomo-video-fps-1080p
front-flash-capability
pearl-camera
rear-slowmo
video-cap
front-hdr-on
rear-auto-hdr
front-burst
p3-color-space-video-recording
rear-burst-image-duration
name
camera
rear-max-slomo-video-fps-720p
AAPL,phandle
rear-max-video-frame_rate
rear-hdr-on
auto-low-light-video
aggregate-camera
rear-max-video-fps-1080p
aggregate-cam-photo-zoom
rear-hdr
rear-burst
post-effects
photo-capture-on-touch-down
live-effects
front-max-video-zoom
stage-light-portrait-preview
front-max-video-fps-720p
panorama
front-max-video-fps-1080p
live-photo-capture
enabledChannels
supports-always-listening
supportedChannels
supports-secure-microphone
mic-trim-gains
syscfg/MiGa
speaker-thiele-small
syscfg/SpTS
supports-barge-in
voiceTriggerChannels
supports-audio-mix
AAPL,phandle
supports-advanced-vp-chatflavor
mach-time-audio-alignment
historyChannels
acoustic-id
supports-spatial-facetime
usb-uses-audio-clock
mach-time-audio-max-drift
speaker-trim-gains
syscfg/SpGa
name
audio
name
iboot-syscfg
AAPL,phandle
name
manifest-entitlements
AAPL,phandle
name
filesystems-props
AAPL,phandle
isc_size
name
osenvironments
AAPL,phandle
name
recovery-environment
AAPL,phandle
node
fstab
replacement
fstab-ephemeral-recovery-data
name
ephemeral-recovery-data-volume
AAPL,phandle
parent
/filesystems
value
name
ephemeral-storage
property_name
ephemeral-storage
AAPL,phandle
value
name
no-sepfw-load-at-boot
property_name
sepfw-load-at-boot
AAPL,phandle
value
name
no-protected-data-access
property_name
protected-data-access
AAPL,phandle
value
name
amfi-allows-trust-cache-load
property_name
amfi-allows-trust-cache-load
AAPL,phandle
value
name
disable-av-content-protection
property_name
disable-av-content-protection
AAPL,phandle
value
name
use-recovery-securityd
property_name
use-recovery-securityd
AAPL,phandle
value
name
disable-accessory-firmware
property_name
disable-accessory-firmware
AAPL,phandle
value
name
image4-allow-magazine-updates
property_name
image4-allow-magazine-updates
AAPL,phandle
value
name
apple-trusted-code
property_name
non-apple-or-untrusted-code
AAPL,phandle
value
name
boot-args-not-allowed
property_name
allowed-boot-args
AAPL,phandle
value
name
disallow-whitelist-disabled
property_name
allow-whitelist-disable
AAPL,phandle
name
diagnostics-environment
AAPL,phandle
value
name
disable-transport-rm
property_name
disable-transport-rm
AAPL,phandle
value
name
no-protected-data-access
property_name
protected-data-access
AAPL,phandle
value
name
amfi-allows-trust-cache-load
property_name
amfi-allows-trust-cache-load
AAPL,phandle
value
name
enable-user-rm
property_name
enable-user-rm
AAPL,phandle
value
name
enable-sep-rm
property_name
enable-sep-rm
AAPL,phandle
value
name
boot-args-not-allowed
property_name
allowed-boot-args
AAPL,phandle
value
name
disallow-whitelist-disabled
property_name
allow-whitelist-disable
AAPL,phandle
value
name
security-not-downgradable
property_name
security-downgradable
AAPL,phandle
value
name
apple-trusted-code
property_name
non-apple-or-untrusted-code
AAPL,phandle
value
name
amfi-exec-req-tc-overrideable
property_name
amfi-exec-req-tc-overrideable
AAPL,phandle
name
darwinos-ramdisk-environment
AAPL,phandle
value
name
isp-horizon
property_name
isp-horizon
AAPL,phandle
node
fstab
replacement
fstab-ephemeral-recovery-data
name
ephemeral-recovery-data-volume
AAPL,phandle
parent
/filesystems
value
name
ephemeral-storage
property_name
ephemeral-storage
AAPL,phandle
value
name
no-protected-data-access
property_name
protected-data-access
AAPL,phandle
value
name
boot-args-not-allowed
property_name
allowed-boot-args
AAPL,phandle
value
name
disallow-whitelist-disabled
property_name
allow-whitelist-disable
AAPL,phandle
value
name
no-sepfw-load-at-boot
property_name
sepfw-load-at-boot
AAPL,phandle
name
macos-darwinos-environment
AAPL,phandle
value
name
set-macos-darwinos-env
property_name
darwinos-security-environment
AAPL,phandle
name
embedded-darwinos-environment
AAPL,phandle
value
name
set-embedded-darwinos-env
property_name
darwinos-security-environment
AAPL,phandle
name
trusted-darwinos-environment
AAPL,phandle
value
name
set-trusted-darwinos-env
property_name
darwinos-security-environment
AAPL,phandle
value
name
set-userspace-reboot-rem
property_name
darwinos-userspace-reboot
AAPL,phandle
name
repair-environment
AAPL,phandle
value
name
set-repair-env
property_name
repair-environment
AAPL,phandle
name
kcgen-environment
AAPL,phandle
value
name
apple-trusted-code
property_name
non-apple-or-untrusted-code
AAPL,phandle
value
name
boot-args-not-allowed
property_name
allowed-boot-args
AAPL,phandle
value
name
disallow-whitelist-disabled
property_name
allow-whitelist-disable
AAPL,phandle
value
name
amfi-exec-req-tc
property_name
amfi-exec-req-tc
AAPL,phandle
name
recoveryos-environment
AAPL,phandle
value
name
csr-allow-device-configuration
property_name
csr-allow-device-configuration
AAPL,phandle
value
name
security-not-downgradable
property_name
security-downgradable
AAPL,phandle
value
name
apple-trusted-code
property_name
non-apple-or-untrusted-code
AAPL,phandle
value
name
only-dmgboot-boot-args-allowed
property_name
allowed-boot-args
AAPL,phandle
value
name
disallow-whitelist-disabled
property_name
allow-whitelist-disable
AAPL,phandle
value
name
amfi-exec-req-tc-overrideable
property_name
amfi-exec-req-tc-overrideable
AAPL,phandle
name
upgrade-environment
AAPL,phandle
value
name
apple-trusted-code
property_name
non-apple-or-untrusted-code
AAPL,phandle
value
name
enable-sep-rm
property_name
enable-sep-rm
AAPL,phandle
value
name
boot-args-not-allowed
property_name
allowed-boot-args
AAPL,phandle
value
name
disallow-whitelist-disabled
property_name
allow-whitelist-disable
AAPL,phandle
value
name
amfi-exec-req-tc
property_name
amfi-exec-req-tc
AAPL,phandle
