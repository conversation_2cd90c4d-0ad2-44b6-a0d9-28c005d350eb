=== KERNEL FUNCTION SAMPLES ===
fffffe000c44a8f8 D _NDR_record
fffffe000c4502a8 D _PE_nvram_stashed_x86_macos_slide
fffffe000c4502a0 D _PE_pcie_stashed_link_state
fffffe000c450292 D _PE_smc_stashed_x86_efi_boot_state
fffffe000c450291 D _PE_smc_stashed_x86_power_state
fffffe000c450298 D _PE_smc_stashed_x86_prev_power_transitions
fffffe000c450293 D _PE_smc_stashed_x86_shutdown_cause
fffffe000c450290 D _PE_smc_stashed_x86_system_state
fffffe000c467b20 D __ZN8IOMapper7gSystemE
fffffe000c457f00 D __vfs_smr
fffffe000c457f00 D _apfs_smr
fffffe000c468030 D _appleClut8
fffffe000c464360 D _bdevsw
fffffe000c4648a0 D _cdevsw
fffffe000c467d28 D _gIOKernelConfigTables
fffffe000c467d40 D _gIOKitDebug
fffffe000c467ff4 D _gPESerialBaud
fffffe000c464238 D _hz
fffffe000c458410 D _iftovt_tab
fffffe000c466588 D _invalid_kmod_info

=== DEVICE TREE HARDWARE CONFIG ===
regulatory-model-number
syscfg/RMd#/0x20
#address-cells
AAPL,phandle
country-of-origin
syscfg/Coor
config-number
syscfg/CFG#/0x40,zeroes/0x40
serial-number
syscfg/SrNm/0x20,zeroes/0x20
target-type
J313
platform-name
name
device-tree
mlb-serial-number
syscfg/MLB#/0x20,zeroes/0x20
secure-root-prefix
manufacturer
Apple Inc.
region-info
syscfg/Regn/0x20,zeroes/0x20
target-sub-type
J313AP
compatible
J313AP
MacBookAir10,1
AppleARM
model-number
syscfg/Mod#/0x20,zeroes/0x20

=== IBOOT BOOT MESSAGES ===
iBootStage2 for j313, Copyright 2007-2025, Apple Inc.
iBoot-13822.*********
iBoot-13822.*********
bootx
boot-device
/boot
boot-ramdisk
boot-path
boot-breadcrumbs
iBootData
auto-boot
boot-args
auto-boot-once
boot-command
one-time-boot-command
