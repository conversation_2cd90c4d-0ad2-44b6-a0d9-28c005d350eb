-rw-r--r--@ 1 <USER>  <GROUP>     0B Jul 13 14:35 extracted/analysis/READABLE_FILES_INDEX.txt
-rw-r--r--@ 1 <USER>  <GROUP>    47K Jul 13 14:33 extracted/llb/llb_strings.txt
-rw-r--r--@ 1 <USER>  <GROUP>    54B Jul 13 14:33 extracted/iboot/iboot_disasm_sample.txt
-rw-r--r--@ 1 <USER>  <GROUP>    46K Jul 13 14:33 extracted/iboot/iboot_strings.txt
-rw-r--r--@ 1 <USER>  <GROUP>   481K Jul 13 14:34 extracted/sep_firmware/sep_strings.txt
-rw-r--r--@ 1 <USER>  <GROUP>   3.9K Jul 13 14:34 extracted/sep_firmware/sep_hexdump_sample.txt
-rw-r--r--@ 1 <USER>  <GROUP>    31M Jul 13 14:32 extracted/kernelcache/symbols.txt
-rw-r--r--@ 1 <USER>  <GROUP>    65K Jul 13 14:32 extracted/kernelcache/load_commands.txt
-rw-r--r--@ 1 <USER>  <GROUP>    12M Jul 13 14:32 extracted/kernelcache/all_strings.txt
-rw-r--r--@ 1 <USER>  <GROUP>    82K Jul 13 14:33 extracted/devicetree/devicetree_strings.txt
-rw-r--r--@ 1 <USER>  <GROUP>   1.1M Jul 13 14:33 extracted/devicetree/devicetree_hexdump.txt
