iBootStage1 for j313, Copyright 2007-2025, Apple Inc.
RELEASE
iBoot-13822.0.166.0.1
(E.9
9)!@
 P@y(
T`>@
Ta.@
4v&@
Ta>@
4u6@
T(@
h.C9
Th*C9
h6C9
h.C9
h6C9
Th>@
B@9p
T({x
R(AH
Th{x
2C9(
R_A,
J88(
Tjzh
+|S
T jh
7G9?
iW@9
!@)?
)@)?
)@)?
S+Ij
*(I*
S)Ij
+*k!
R(I3
h~S
7hz@
7*ih
**i(
Tj~|
3+i(
TLa@9
7Le@9
4h&@
Si&@
Tkzj
rlI*
Tkzj
SHI)
S(I*
rKI)
SuI)
SiI,
SHI)
R*I(
rLI+
Tjji8H
7h3@9
T*ih8kjh8j
*jj(8
RH+B
TBkyx
%J))
A@9h
%J))
 B)"
hJ@9
(+D9(
HoA9
IgA9?
T)+D9
5HsA9
TCg@
BwA9
}B9?
)!D9?
T('@
T*%@
6@9H
}B9B
_K68
gF9h
gF9H
gF9H
cF9S
kF9h
OJ9h
GJ9H
g@9h
)Gzc
rd"Hz
Hz(f
Hzh.
HzhN
Hzh,
Hzh.
HzHf
Hzhm
HzHn
Hzhl
HzhL
g@9H
jx8/
Tazt
(1#9
(Q#9
u9hb
QJE*
u9Im
hjw8
rciM
T`2@y
h6@ya
T`>@ya2@y
4`>@ya2@y!
h6@y
Th:@y
4zkv
,B)
@y*1
:@y+9
'@) 
ThB 
@z 
h"{9
Rh";9
(e;9
(i;9`
Jii8_
@9)]
a;9 
i{9h
Si3I
T*E@
J58!
(A@9_
#@9?
@9)i
;ih8
T+ih
T*ih
T"{x
T"{x
Taky
_8n!
*a@9
6)!@
TnrM
*h1B
Z))=
!W))=
K!C)J%B)
)!@)(!
4hrM
ThrM
*.N,
Tyz:
Th{y
(%@)
@6;<
h6Q;
q3/
TQ{p
6q{p
TV{0
KQi+
TLi+
TLik
Tp{o
Ts{/
KPi+
TLik
)@za
T*ih
TMih
TH{u
TJEK
r)a 
!r9I
Thzz
Tjjh
5jih
Rjj(
@6`*
r)q!
T*}C
Qji)
G6l%
T*}C
Qji)
A*-
T"{v
T)kh
7h{x
Ti{h
6h{w
)*)u
Ti{h
Tjkh
7i{h
7h{x
6h{x
Tjkh
6hB_
xI{(
@9KM 
'f)?
T(sM
D)B<
FA)B<
@)1"
A)B<
FA)B<
@)1"
6({u
(@))
(A))
m9_D
T+{h
T:{(
vz(x
T+kh8J
)a*)
j%A)J=
*k)@))!
rJa!
)!*)
Th{@
#A):
Th{@
TH{@
T!{z
S`J(
S`J(
S`J(
T!{z
TH{@
Kw3
T!{z
S`J(
S`J(
S`J(
KX3
T!{z
SX!@
Aqd)@zqS
*,}
@9(
Rb*@
![9H
T*ih
vb@9
 ih8
 ihx
r)ij
r)ij
5h"@
r)ij
rh&@
Th&@
r)ij
r)ij
r)ij
r)ij
r)ij
IyA
(!69
1@yi2
1@yi
1@yir
TKQ_
T`r@y
`r@y 
B)c}
)BzHq
 Hza
9v^@
q")Fzi
q")Dzi
6A9)
jp9h2
Q(I*86k(8
*@y(A
5v^@
"B)!}
2@y(A
TLMt
hj@9
B9?1
h~E9
2@yi
)[@9)
*[@9J
*i@9_
)Q@y
Y@9)
)@y*
bk9h
K|S,@
S wE
T`*@
T`.@
UyKo
R+ij
*(i*
+EAy
_By
@q51
R+ij
2KI)
Tij(
qM*m
AF9h
QAx)
qAx)
jl8pyp
TPyi
TPyi
!@9H
A@qa
S)i~
V@9h
Y@9h
a@9
`"qh
hv@9
@yA
}@y_
5J!@
xh*@
!@9(#
@zA+
~@yI
~@yI
~@y)
~@y?=
9h&E
Ti"E
}@y_=
%B)?
%B) 
y`<@
Tlyh
Ay|H
Ay%H
Ay"H
Ay6G
Ay,G
Ay!G
6H{h
$C);
ByL@
7t>@y
b>@y
i>@y
i>@y
Th"@9
h"@9h
"@9h
 @9(
S@9@
3@9@
3@9`
S@9`
5h&@
5h&@
2j.C)_
@q)%
A@q(
l6M)
l6R)
rk2C)
k&D)k
5h&@
H'@)
7hv@
7hr@
6g>@
6gB@
Qhr@
T`z@
4hz@
)Q)*
Qkr@
Thz@
"D9h
_)Ikc
T+}J
C@9h
iz8h
T(k|8
"@y)
h"Gy
!@q(
Th:Gyi>Gy+
TiA*
5h&@
K0@y(
H@@y
6KD@y
TKD@y
LP@y
LT@yi
kJ%
jH$@
JX@y)
J\@y?
KvF@yuB@y
#u)h
4h&@
*AzJ
Th&@
7($@
*@@y
+X@yk
T+T@yJ=
*X@y?
)@@y?
7*$@
,X@y-\@yJ
_!.k
)qAy?A
Kik8+
Kik8+%
T)$@
4)(@
*9
3L5@
*K1@
*K1@
S@I+
kB@9
@9)A
@9)!
@9kA*l
@9k!*l
TaB(
vB@y
*a|r
J@y{
J@yM
!BzH
T`zv
Th{u
j.8
4y{u
!@y1
Y@9,J 
5hB@y
6nB@yn
*nB@y
F@yXC
F@yh
h@69
6*m@
F@yh
B@y(
T {xx
hB@y
RHmt
)]@y
hB@y
@y)A
Y@yi
T(G@yh
Y@y*
TjF@y
B@yH
B@yi
Thyi
5(@
@@yI
TLyi
Rmil
2mi,
hId8)%
Ti!@
jQ@9oU@9qY@9a!@y
!Jzb
TLyh
@9m!
Thjy
7hjy
Thjw
@9k%
mihx
J%@9
R(s]
h.@9(
'E)h2
a.@9b
4ajv
hBn9
RhB.9
T`3_
UJ9?
QJ9?
TK1@9k
yKqA
7@_B
3kB 
hf@9
*A@9*
'Y9|
;@yw
&C);}
:@yJ
9hb@9
`6@y@
@)K=
T(@
G@9d
V9GWB
KJ}
HShF
Rh&@y
hF@9
*Q@y
T)Q@9
Q@9h
 @9)
S(qH
hr@9
hr@9
R*i(
`F@y
`F@y
`B@y
`B@y
`B@yd
`F@yW
Th&@
Rl2@
T) @
T)0@
T)@@
lih8MI@
iw8*K@
iv8*
jv8(
@9mI@
t"F)
Tlzix
ijjx?
T*yh
i*A9*
9hF@
Ti:@
c@9S
+@9_
1d(@
@9i"
@9)!
TB@@
Iii8
Hih8(!
I`8:c
@x)}
@y?!(k
*?C 
TH)B9
BD9(
ik8o
i+8*
h*B9
@8+q
B9*|
ThZ@
Th^@
Thb@
Thf@
4hj@
Thn@
Thr@
h"B9
4hv@
i"B9I
_8K!
h2B9
h6@9
j2@9)
B9)!
?@@qb
h+B9H
7h/B9
Rh#B9a
h'B9
(B9h
b3@9
5b7@9
b;@9
5b+@9
b/@9
5b#@9
hbD9
@9]a
;@9`
@yY#
@y)%
iixM
#L94
#L9I
jn8.
ny(x
)!L9?
Jik8k
ji,8
i)8h
ii8h
h"M9h
L@9J
L @9J
L$@9J
L(@9J
L,@9J
L0@9J
L4@9J
L8@9J
L<@9@
!TJs
4!TJs
KBx@L
Td_D
!nB80n
8pnD8
Te_D
 nB80nd
!.f80nd
8pnD8
QoBDnE@
Te_D
 nB80nd
!.f80nd
8pnD8
TBx@L
Td_D
!nB80n
8pnD8
QoBDnE@
e@9?
4h#@
5hb@9
hf@9
`j685
jh8?
(a.9
in9(
aAk@
6h"@
4h6@
1@9h
))A)
 Yhx
))A)
{2@y
h:@yi
1@y)
)A)+a
)A)#
l&B)i
i"A)
1A)k}
4h*@
&B)I|
)A)+
T-e@8l
TMQ@9l
TH)@
TMa@9l
T*!@
T()@
T)1@
)A)+
2@9i2@9
5h&@
T(@
 B)k}
"B))}
T+ih
*ih8
Kih8,
T*ih
Tajt
Th"@
/9fU
/9@*
jsx(
T['B
h2@y
v2@y
4h:@y
h6@y
5y:@y9
Th:@y
6`6@y
h>@y
@y!A
R9?!
i)8)
@9)}
@y?E
IIy)
#@9t
@yhB
3Yzb
qB)7
(ih8
(ih8
(ih8
(ih8
(ih8
T)hh
Kik8+
Kik8+%
_h(8
(it8
#@9(
#@9(
#@9(
T)}+
kk8,
Mz0}
TH'@x
j989
@9
Tkjj8
i*8J
i)84
T)hh8
h38 
Th$?
T`ju8y
4`ju8^
RC@#
R(C@
4j:@9h
j989
j989
j989
TI'@
6A'@
*C'@
jh8?
*A))
&A) 
&A)"
qH/@
5I/@
4IC@
&A)(
THC@9
9A+@
j(8Z
TH3@
TA+@
7h.@
.F)J
*F)C
THC@9
T[sF)
I+F)C
&F)(
*F)B
&F)(
jj8k!
it8E
B)I-
yk}
R))@)-
.@y(}
_x!@
%@xp
`%@x`
T*A^
TJyk
T*A^
TJyk
'@8>
@)(]
@)H!
SJA@QJ!
3i|P
(I*8
(Y*x
*1@)
Kykxj=
TVky
T!kx
#@9_
Tkzj
ThV@
ThV@
i*8J
5@9I
)1@9v"
6@9(
Thzh
y!Y(
@yz
Th{y
0@9?
4`"@
TH#@
UF9H
Zh{9
)#@)9
4C/@
-C)5D)
AF) 
7H@
7H'C
7H'F
4H'@
7h&@
YF9H
A@9?
HC@9
R)'*
T(kw
gH)*w
/@y?A
/@y{C
TjB4
5b.@
5`F@
T5?@
5 [@
5 [@
T [@
5 [@
yh*@
4h.@
5`F@
5`F@
h:@y
53[@
cO9(
jh8_
cO9h
Ki(8
TQu~
1@9?
uuid
trap.S
Invalid SP in exn handler
kb!Tz
k"!Tz
T?7@
-@9I
-@)H
1Iz"
,@)k
TKA+
,@9
qD)Cz 
T,{w
X7-{7
H/@9
4H/@9
I+@)
TC#@9D'@9H/@9
H/@9
TH/@9\
6I+@)C+@9
_xtB^
_@9*
j@CJ*
@9iB
hB@9)
Tm1C
Tm1D
Tm1E
Tm1O
Tm1G
Tm1F
Tm1H
Tk1B
@9( 
ThN@
9wF@
Tpyp
Th6@y
1O9)
(3@y
(3@y
(w@9
T({@9
Th"@y
|@9(
B9ir@9?
h2@y
Th~@9H
h2@y
T?C@q
+ij8h
*(i*8
TKyh
TKyh
B9(#
Q|K4
_+Hj
"Ayh
$Ayh
2Ayh
T(CJ
4h*Ay
0@y)%
hJ@9H
hB@9
ThF@9
4h:@yH
B9i"
`N87
@9(q
*_5@
@9(-
p@9_
x@9h
7)'Ay
4)/AyI.
T)ywx
 /AyA
2Ay?
Ti*Ay
T`>@
`*Ay
Jk!9
@@9(9
)3@y
B9)!
!3kA"
@9H
1O9)
T(w@9
(s@9
)3@y
6<7@y
4(s@9
B9-s@9
B9)!
**7@y
S?+Ij`
h2@y
Th~@9
h2@y
s@9h
8@yH
B9I#
h6Ayh?
fhj!
S()H
0@y?
0@yk
4@y?
0@y)
TLyk
ySy(
B9k!
*,7@y
D@yS
@yhB@yiF@y
mN@y
yLy-
!.kH
T)!Ay?
hF@y
T(@
@@y?
L@y?
@yiF@y
TmJ@y,ym
oB@y
ymF@y
!-kb
S?aEq
T(#Ay
T)3Ay?
T(C@
hB@yiF@y
yhF@y
hbn9H
"@9b
Rhb.9
an9H
2@y?
0@y_
@q@
'B)K
)*(yH*
*B))}
EA9?
@A9+
@A9h
T_C 
Thju8
Ri*@
Ri*@
5h2C9HE
(-HQ
K@9i
S)5}
;@9h
2C9h
B@qi
S)j}
bB9h
h"B9
7h&@
7h&@
R <@
@y"<@
!@@yb
5@y+
js8(
Thjv8
T`jv8
(i48
AD9H
i)8+
(#D9
T(#D9
i)8i
?@9h
bE9H
T`{z
`@9h
bE9h
)=F9
4`RA
=F9h
a@9i
a@9?
*a@9_
*a@9_
6a"@
T`"@
RjZA
7`n@
7`*@
T)MA
T*yh
Ka@9K
K"}}
$G)?
2F9H
jh8?
8hrA9
4hzA9
F9*=
4(c@
TKii8
T_h(
i2@yj6@y
4w"@
Th&@
7`.@
,*J%
s@9(
#Q8H
+**%
5H#@
4H#@
T?@@
Ti"@
T?A@
T)P"
ThzA
T)P"
T)P"
ThjA
_#(k(
T)P"
@8j!
"8ki
I!@yI
4hk|
7`NA
T`NA
*f"
 6hB@
5br@
T`n@
@9hR
hBF9
hBF9
5hv@
T`v@
5h~@
4h~@
Thz@
4h~@
Thz@
h"@y
*ij,
5b*@
T`&@
87`B
g6h2@
T`.@
hFF9
?@9i
_9h(6
@9nyp
@9nyp
@8nyp
TMhi
Mhi8
!NB( nB(`nB(
JHj8
Tk$@x
Jzh:
LklxM
Ak#(
5 .!
!( .!(`.!(
80.
B80.B
!".0
B80.B
!".'
B&.C
TlJi8
K%@xl
@9*!
i"K)
Tm2H
ThZB
Tl2@
+ikxk*
Th6@
5h&B
T*i(8
T+i(8
T,i(8
h2I)
Tn6B
Tl:B
Tm2@
Tn"@
Tn"@
)h"@
x^K)
Tk*H
Tj2@
C@yi
Th6@
5i"B
T*i(8
T+i(8
T,i(8
j.I)X
TjfB
Jykx
kynx
Tl"@
Tk*B
zjxK
Tl"@
)h"@
@8Ki,8
TI}D
@8/yn
jh8M
4Eyhx
@9)%
@9J%
@9k%
jh8@
ThB)
@81"
T(@(
zgI)
@ymklx
Rnkmx
`NJa
5jG@
Viy8
Viy8
Viy8
Thkt
I)hG
Rik*
Thkv
ThGC
Ri[_
Riki
+Kj{
?yk=
+Kj{
+Kj{
?yk]
+Kj{
a*h
ThCC
4i3@
@9){h
?AyL
CAymjlx
Rnjmx
`NJa
uztx5
Zit8
Zit8j
Zit8j
.jo8
RC@#
SC[j
=c$.b
TP@`
N4B`
Np2p.
5N*>
1:0.
SS[k
1:0.1
SU[l
s:0.w
80.
TX%@x
!8kb
Rs"8
D"qa
,QJ`
@"ql
6-F@
6-&@x
"@9(
6@9)
>@9(
.@9)
:@9(
&@9)
2@9(
Z(N!x(N
Z(NBx(N
Z(Ncx(N
Z(N!
Z(NB
Z(Nc
!n@H(N
=@H(N
T($@
T($@
@9K!
@9kA
@9JA
+i`8Li`8
Ki`8,i`8
RKh*8
_h*8
_h*8
RKh*8
_h*8
_h*8
T?i(
jj8k
Kih8k
JKi(8
9@)
Q$@)
@4A)
JLC)
O D)t
F,E)
T@F)
Mil8
T+x)
Tjz(
Tz"A
"nB@
jhi8
hi8+hh8)
jhh8
hh8hi8j
r4@)
T8O0
o2:an1:a.
:a.T
W@)s
n~)<
n~)<
,A)4B)
<C)0
T8O0
/2:a.
:a.1:anT
Jg*GJ
x@)Z
RIh(8Hhw
_h(8Hhw
_h+8+
RAxh
SDxk
3Dxm
3Qxo
ic8?
hh8?
RCx(
 @)*
X(N!x(N
X(NBx(N
X(Ncx(N
X(N!
X(NB
X(Nc
%n`(
H(N!h(N
H(NBh(N
H(Nch(N
H(N!
H(NB
H(Nc
%n`(
}Ut]
$o,
vRQ>
8STs
LwH'
= ((^v
NA((^rB
Nb((^rB
((^rB
 nx
= ((^v
NA((^rB
Nb((^rB
((^rB
= ((^v$
NA((^rB
Nb((^rB
((^rB
= ((^v4
NA((^rB
Nb((^rB
((^rB
uVxO
1VjOk
4nc@
TCOm
uVxO
1VjO)
4nc@
1TCO-
uVxOk
1VjO
5nm9
4nc@
QTCO
uVxO)
1VjO
5n-9
4nc@
qTCO
uVxO
1VjOk
4nc@
TCOm
uVxO
1VjO)
4nc@
TCO-
uVxOk
1VjO
5nm9
4nc@
uVxO)
1VjO
5n-9
4nc@
RRRRRRRRRRRRRRRR
oCh@LB
nAH(N!
!nA`
!nA@
oCh@L 
nAH(N!
!.A`
!nA@
oCh@L 
nAH(N!
!nA`
!nA@
NGH(N
'nG`
'nG@
x(N!x(N 
x(N 
h@9h
H`@9
ij@9
9hb@9i
ij@9
hb@9i
h@9h
`@9)
d@9i
9hb@9i
Rhb@9
7@9ib@9)
9hb@9i
7hb@9i
9hb@9i
6I=@
T(|~
ij@9
hj@9h
h@9h
hj@9h
h@9h
(ihx
+@y)
Thf@9h
7hj@9h
"@yi
_xl!(
mii8m
T|"A
2?@@
aErO
aErO
fena
enar
fpoa
poar
feva
evar
fxfg
xfgr
fpsi
psir
fois
oisr
fpmp
pmpr
0pcd
fpcd
2pcd
pcdr
1pcd
fpcd
2pcd
pcdr
root
storage
rtsC
revC
revC
pdcC
xehC
ktrC
xbmC
tscC
AgrC
8grC
MgrC
mitC
aehC
vr8^T:l)U
U8*T
]o,&
h&(z[
=Mk`
p<5a
,f>'
xj)9
sEy8
=*\\E)
}Q_iz
^1kW3
E( V
I0$r
mNrS#doMngeRSHLCukskUKSWomla
gaid
cebi
tobi
tsti
trep
tmbr
tlhp
cebitobilefcrmmhtreptlhptmbr
foic
fumt
oicr
umtr
snar
fsna
lopl
AeeQ
j313_8fstp
RELEASE
<EMAIL>...2025/06/13@23:31:21
2de490d128ffa6580691e2eccd2bdbe4
bvx2ij
u{wq
mh[8G9
nKO/
>XZ~j
\.+
B ]Y
7P'A
 vS8
=LpE
tpae
[H8x
,K8o
-Lc0
f;9ruc!
8~@/
lmqa
rb:dg
av$Z
}o?y
KMJ}
GB3mY
kkGS
`1%l
Bkf*
f642[
q!tS
dzSnQ
GIFn
0! )>
X5=b
\RKUf0]e@R%
hZM;
NHZ64U
:4eY)
Gs&J
}"Y;H
s|3'EnW,
p$Ji
m]^I
d27]m
p'MR
^AlSD
nOid
=h0q
O^nU#
pO~M1v
Ml\=
<WE@xG
Tt<$>
i;: W
W06_
}+R:<
5`I^)
aup<c8
$Ah3
9lEFg
@#t=
=H=0P
]F|g
b*q3
IG7BG_X
V62E5"
snEB
-Vc/
C0uYs
:FmG*^
j|MW
a;2SR]
s}@)
l0UA%L
b`Aa
Wl$D
Wa5pt
0B!!
V{|A
n|wu
 q=>
is!3
;RQd
=Wp|
_03w
)dp ;
E9#V
"p2K
&:&m
vEOd
 ]_iGF
/EV)
~I3#
bpcl#
D&`$
.*PbN
Vv=U
2<KeT
0$|$
,5S !
J0Ly
inV&}q
p@53
x E?qh
7~Q`e
Di\y
TOSnO
ppo_s
[:U~
S]SO
76!?
(,Jc
dM(~
l<=%
KLxz
Q]@
y\8e
T{?=,}
.INn
LXW
<Rw1
bAWo
!,[=Z
MzZl
jSye
,^n+
\t}
eB[h
?d7h
9\>1
wxVJ
}nc{
5 @|
%,bO
4WYK
ki]#wA
V<f]
fhi\
P#g.
 _:`B
#9={
u"i
KU}!
%=7&
3:{%
Yz8B
G7I`
'{3pbt
x!\v
c$:j14
!tM[
5dh?
3eTZh
"#Wau
cLsh
P6?M
tXvD
zdZ
2g.R
26EjQ
y~TaD
(}xCQ
Lb-=
8ae,
9c/B
)vnUS
rR2<
%p$*
^7Dp
mL2"!R
Y:+TLM
ppy3d
I-elD
w({h
D!(zV>
&0pq
7<!6
F].1@-L1
Q{U^
Ykqm
.-.Eh
mTW=(y*CX
AT{^N;
5=DOL
!f7Ges
i::|
%@%j
2?8M
?q'E
.?JQ
[GHW
L[>04%
%tXG
Ssb:$*[6*
{*ROua
n1LO
slUJ
@Ga*
CYze
(X:C
S8<YY
b98C
lQB}
rel
"YMw+
R#n1
PQgv
oThb
L`UD
B]`W
z_S"Z
BITJ
f`Sfr$_
P^..:^
{w0u
%gLQU1
Wuz0
%BOR
'^S%M5
M+'3
tu%j
,yif
q,^3E
dO~W
w&3@RY
r2g)
$N$l4
Dwcq
`c'w&
B@t<
HH,X
9kJ1
Z%eV
*zq0
H?e
.KWS
}ONn
o[Nk
y<Gx
Pq7BO|
a@Pg
M(7[rm
ufg\
?.Sz
\RII
NM.N
ZWjn5{_
SECP
AV-h+
("(F
)BhY
`'%{
C+N}
0itCK ]
ZMX_
/zUx
O.hA
jA]l
X.z%
YoB<
>pCD
k}C8
0V*~
Qn+s
hxuf
66-5S
F\/?h(
&#xw'/B
ad(K
C{Sm,
eJQW
 3)"2
%]9#
u2/z
gE%x
7%!:
<\2{
c"./
1#6O
'Ezg
e<lF
R[A5
lt&Q
5\/X
Cg~d
A_pr
aK]
U-35
(z *
Vme9H
!0K%
3Lo<~
|]\e:`
2Fo<
rm=K
,x][v)S
>ApPQ
&EQf
t*oP$?
2&9o
B&z~
eBY`.
jG|<P <TQ
QU23Ou
By#{F!
&>|S
\/;z
@z2h
LF'V
=W}`W
q[Zm|
JFTn
LFDl
@5RK0
x@l[
!,yp
wl<$H
Eo-B@
hD&Y
o<<KP
$=:@
!2Wh
A8}6Z
J@`Q
ARx_
+oa`
YVLEb
3 t6S>&a
y^E#
q'3M#
(}KX
%AKv
-k|Sov9vh
vHEld
fz)=
nshq
a&D
d]xF,T
I64Xf
[V<R
-DWw"y
'sEF%
[n5&
porMG
:(lFNu
4e9D7)
ukc6y
p3a,
I8}"
b,Kna#
a"!?d
w@^W@
Jm7U
9;~?
$7e_
1Tn0
2\Yb
@ 7t
]KsF)
,c'q
GVKw
c PG
g&to
[Q/{
sM9'
KMVs
P8ld
ZFr&
<Y7H
>Zs\:
s[a+
Z_gS
=xR4
u:"vB0"
8C(VX=
%O=`
k;c5<J
RAL`.!
{:V#
j;yT
lCMq
,sRc
tTXT
1}!3
ivpo
;'MW
Ts}*
U|/V
Q)kLFd[e
5Vx`
tt7o
"W`<
KnVM
9NK%
eza~fy
kU](
T+K=U;
%SMo~
]#n;n
zM=L
e>^fi
k3ff
&jxR
9}hy&
.khxNpe~
IP*`
e:Kz
(9 X
z_yV
IH,?
6(!|
A~Fp
j)'?
_M'*
}{N+
mZnA
!0:e
FW+d
'|&<>}
(@o4L
N]G>
NU?xc
ZBN&k|
pprG
C!\_
Nf#e
:>mp
Uc5&
RQ*r
_$hV
e5!K~!
DMHI
T{T9
k1,y
.V|$]
GITL
T'Qy
$-qp J-}
Zv ;K
?EoE
K!bH
0K%G
y7$/
%R$q
@zZ6h
[a~kI
*J3\
W[_K
a3k~
bZ$W
2H%-
*dV,
bOP7
N~CK
SRK2Z
L:bo
VN5i=
Qqv:
18"J
k0K2
eh<:
GgKv|xv
r)N=
J`(n
gt71L
Db%\\
PRlj
26hb+R
a)9JQi
{p7W
VRF]_
z+d|
G>ox
<?f|
d< Z
p#CO
$Y,9.
v6~?x
B7Dr
^bZ*
eJHs
$A{Pu*1
@W\&
lL.
r`1:{
%,~@&
RN)&AbH
={
_b)e
0B?c
b8b((P
INS1
c:K$
W,mO:P
a~^X?2*
cDH1@/:b
1}@^D
h]<O&}6
"jA4
0d<
yn`$
&#eF
Ld8&l
x*KG
-KONb
F88 -
`^<cu
,e8td
hBd2
:0&EFhP
a0@*
0oEb%
v`|r
aDr6
S2'm
F(XX
VAD8
 I i
E=Na:2L
ehD;
34c2
N)6"
cu"M1W ^
'X(C
1Ht'0b
%)>a
!8r-L
]7lx<
}u[L*t
zV/C
sWC4[
 H=hH
|F?*
sD+n
g<E!8%%
pH[j
735!
*c63
/V'
;Lo"
't`l
RCK^
yR`A
E.mp
x#.;@
.'k
@+#L
wIUl
ntdK
xg21i
SJ`)
#^YF
d_Ab
%-q.F
CI|n.$
?#V1q
@M`v
JEoH
:8C8
Itut
GZ||f
,*l'
N4d&
8,@Y
J)1@p
hq#;
wf\dJ
7h F|T!
AHxBB
L,)8
%3`kA
eOln ,
qYMa
Aa p
_H" L
;H8,
%|gD
/TxWc
g2^`
%4u|
)KA_
7<pS
CILhE
/vLo
<jlB
,Scn
e1HV
yEq.
k)oE
3RGz
ECL=
_`H!
rFDPx@l
-m8x
m'9.
 gp&8 
x\C3,
24KR#
o!r8
&["^p
5z)b
a>TN
;W4D5x
X^3DT
K=\iNQv
KNBf
bpbi
$F`4#
qSU'
z^RA*
dVc$
Ah3$
p'@:z
6]<}h 
#'7d$+
-b$Vb
od9&Mi*GG
aN9EO
a"wR:
895q?
m-5%p
>C#Y
*qRg
2En2
@#)"(b
%0l$
/XEw
Nh:,&
s]bC
CTFMOW
X<x,
R0s4C
V!&f
d0[B
yCRZA
+zQ<!
\#06
!`sH&
2X~2
7$E@@
XJ@b
2t#c
blO'z(
!h'P
}ax#\0
*.(mLlg
vD  
bfNH5
asaA&
6p(v
d$N$
DT==&
R"EN
,rS6
h_BP
@P(=
V$(j
+7(0
-Ru@
-c<R
A>0(
00HI
vmK6P
N5+M
1D)
]Ks?
"W05
 xb*
!YIJ
xL8>
je :RM
'(na
\nS*
Gfi*"
Vlv$+
O3G 
pK1~*0n
<->;:
zLjE
$T>6L
7!H?
td>nJ
7|xB
?F4Q
jS(Kl 
WR7q
SD 3
WV0L
mrV&J(
h0[a
.dHbc
1(=C<
}#9~0~aE
e.F5
kXda
y{Jh
WV),N
.Y*\
j~3S
@\ED
cG5?X
B,A 
bFP$
B2"tzG
A?CGe
x,k4
Rbr%?Z
bvx2
4mTMU
z'9E
b-B@
c-y-
MJ"N
r|`?*
H;W5,l!
&qB9U:)u
|l)(
Ra4S
i1! 'ja>S
9$R/
n`fBx4
^?ur
NEHtI#
aw"Yx
/@_1
aSI-D
}FJ}U
3iKi
%}l1>
AKR#
*hBM
;'tX
ia^G
S\R7
;c3D
JS_Y
p,=u
/:uP
#L>c>
Ed3a
r752t
4B6?D
(P[)
2"l
\p)c
] 0L:
imt3H;
-#yQ
<`7d
T_i^]_
O#O$
+Kg6
~x'p
grh2
D;ZO
:x2%j
%{&u6
{RgL
Fl47qN~
WKO"
f:0S
Twz-
v~(u
hZK 2
0PIJ
:COTPI
C<f"
2LF~3
xx_q
q,5Y
r8Vq
K&@S
q.<E
D'Y~
SYBG4P
H4-t
c PD
@*F!U67/T
)5L=
o@KB
Zg-M
D_)N$T
m#%A
iN/1
tU]n$
G4/
TG,FA
<hQ2
(!,7
EZ?+
M&BIHw
E?2~y
g6[m
dy_d 
<\$&
Hr%}
J%np
@:<#
FRk
irS`(
"?@Ji
Wz)P
/n"!
Zie 
C}kX
V;(ko[
fawN@
*=f4
9Ssd
7D#A
)#0/
|zSC
w H~
h|O.
h$\`
k0FS
hbtb
LC@S
:5Av
T|$:~>lU
\Crc
G_G9
{.,a
MUsL
WxBs
A9oHJ
ValCL
gu*!Z
qBO(
I\CR
wAnvgw
~y P
9Y?p
E*/);
QhSxNbt
1~HY
[tIz'
o=x{
uP|HK
`)0j
\@n$
%q.C
#D{!
^IsS
nM^L
ExD`
jdBH
T\XK
n0`m
%$ -2<x
a"uz
2;FJ
^%6l
-xa[e
7YE)
v]>g=
Ohd#|
e6Oa
YU'*$zX
_U"gkG
=s>#
p1eW
]uoI
SiAP
Lk3fU
~WG6
7zhB
q0"(}
N@1|
SJ\y
"Bu^k
jG>D1D
Q'<A
IJpR
Ur-?
Q}w{
=B0_sR
_{W1
;(MT
W?l</
sXrb
V]0-
Gt],
bcuk
N"4.
4<=X
2\0
BSiX
ic?d
|ECYp
|e)N:
w["?
xG?2
eNO~
<2sC]
1Xk|
<T2f
&#TfH2e"
baAL
'W=D
V5 (
"J/_
(s-X
p(X!
w)o'i
9ko/~
+;T`
LNftJ2
P]:t
C*2Z
r[s+x
:t|6g
Y#D+:
]oW5D
n.I7
b+/z
wK8N6
hc8s<
Xj%i
y}`MI
c1y},&
x+$7
U:bH.^
C82D
?+#x
Xm_?V{
`5J`Xn
CXJ{
D!PePL
AaEfU
RwR%S
'RK
EY`^/
^!]4C
IPR}AC
gkl{Kk
Fspt
+g&a
q~XQu
;g<9DD
$2>I
13wFJ=
np|\
*<Wf
S4}eK
s^7'a
xVs;
!:h[B
qHy8
(lM8y
66w<
b5e4t^
?E7
03n]
;^lMw
~{C[
)m&^
Y<9}<
q0@{
3}Cpi
tXI4
q}7x
n[EV8,
>Z'>
okiv
3@jQ
@'*d
%iK9
F61G-$4
GDvR,Gm
zlq#n
50=D
oU?E
]rO(Y,
c1Wq
\?$R)
_)z/
&9DeM
t|t5
F,MO
&/!=
W0QR
U%R%-
)(YnzZ
s35:
vYS!
EtzN
97dc
&<X6
uX#K3
s@w9q
A8hU}
mnIdj@
H!6N
#T5P
PVag
*j^8
{3Fj
8Up^L
9@3I
E(f6
P9Y*
 7R r
]9KS
_PA
)^INr
 q;H
%D/H
Y4"#um
K(tl
*oE}
rbQ
(}w8
$u--
ev$y
Dq,Y
R]mE
7~> 
FS\H
S}Bk
B/O2
E+8{
J[FI
U&()
1:]':
)AC 
_k_9e\2E
h`W&1
#%Fhj
="i:F:j
S/uy
[FLQ
>:?C
^4or
(jylt
 ;pL~
Y&<&F
]B1>!
^K i
dZnf
vX!Z-^
$@GQ/
u58wAFdh<
EPQo
z Jd
G%Rp
S#]S
o?5{S
~n)
.Wu\YO
UA,.
*>d%T
;^Oz
>UaA$
Y6==[
-7%:
WV~D
h8)_
_GqAd
-}:3
|I1+
##_\
$:uy
`iAyBQ
;KqY
&w0~
v?Ez:
^%vr
KTq21
edO$2%
r."C
4upy
Shgs
P/q6
Z6-[8
rM\2b.
eBImK
2V7 
S#ID3@2
!2V{S
jBMWr
xO`U
NOd8a
1l*"
E#oU
J53wYiRV
;vK_
/MK1
=Pj7
&lIk
xigET
WS=%
7Ed5
l3J.
Mg+>
 |+,
D\l$
liu$}r
mtfy
Ii-6o
eWn`
lvPCB
CN(7
k-d6
<IbD@
9-H`
<vR<
dSV/pj
TR5p!
>m'1
1<J7
gb/%}y
ee(A)
.FBu)
HM$D
a"E,
k9GV
]D%a
g49!@
yLi2
a~`b
yi#f
IQnf2s
95zl
7usc3
07JS
UyyH
I^b|
8dAe
e30XU
X46#
0|9*RP
3Ok-
n m'
0E>8?
-`Kx
O"N-
c?Sg
3Qi|
3$Ga
"=RD
W1iB
bFT?
f%W!>
lFsH
yDb
Ct3)
r)D5
QPl'\
W<8>
:Iq%
y;+l
Nne4
ENmU(
D83DrsR!
{iDh
>^gL
&9Qc*A
#&%.
BYfx"
H8,b
iyEM
w1\~
r@I
<ILu~d
<6LA
% cD
a_Ro
=]m/b<
TICa=
W`_
IO9Mk0
ef| I
1YD,
A'k=
BD>U
8+T8Y
?c8B
gqgG
V#g$BT
uDcp
/]9'
!1PD
^xJt
@a4YD7
diz#
B$f|
3l.|
9j9f3Gm
`,GP[8R
&)!a
cN]J
`hw)
T:+$
t"d@
A+o(O
VH:40
?TeP%
\ql0=
%dL%
|PbT
Qc0AKb
yd@1//E.
KHK.
W'n!B
}4A`
i53@
#WKT
/ PaE
Dw 8lJ
Kd*$RY
MZ%g<
;O=}
a%x"`B
@S/,w
57~A
[d4~@Z
\Fy^oC
Sh2F
6suHXg
/o.-
'cKM?\
JK=d
0,:n|R
e'*ff
D"~UJ
h'q$
$@Yx*
{H<l
B8|B
S++{
5j0py
wq47
,-j= 
yO,y
RJ:*
&biX
CBwR
.3lD
${B 
ECdN
`W(
|"T1
BW"?D
U@SY4**
-RJZ
)#$&2
CFP"
Y~`W?
j@8A
aYf
aa+Xf
'0&sU
Rqb@=
 %p8"
MOQ`A
(snx
!6Up
Z.T]
wQbP
YOl,
2lO+PO
EYQ^m
cV>S
Z/$`
:IKb)
GLfG
b4,9
a3q
2y68&^
`n]A
>7Au
H>G(
34D!L
lr#"
&K<N@d
GiKd
b@CJ
^"")
=aLF
T_4Tq
F1tGi
v0PZ|R
{`]1m
&`hx#oq
1j@E
c>9F
X4OVA
qAyb
%Dfv
t!5o
Q`? 6
Do% 
bBM
^e"$
b wd
9gh 
<Y RW0
`Bwp
0(F6
PYtH^
O,N<
v,L[,
+!`#
g;xeb
_4qI
10oP*
>0F!k
@rWt
im"u
?h+P
47>MY0
;?t%
e!|1
R F4
% *I
yY'H^
21aHf
<O3
^"R"
1Na#
]P_N\
re4XQ
!DLX
Q-#&
G#HssY[
qc&Z
"@l.
(`FsJ
:G6o
52=RGN.
>1m
#Vn+
1P$eH
IeSP
5ZOdn
pzPt
K%7D
B&6>
5b%'
] yY(
.4`e
;:!
!u`PB
{`nz
E *T
C@$H
$sA9C
x3=k
G4k.
]+<f
-H?I
dj04Bv
%OSC$'
d+l
@1p8
(6Te
6<}l
y<rR
8>^:S4
G&x8
Bp)@
qff5
;*o86
w|\$
$(cP
]so"
fS ,
0W3)F
XDS
F*2zB
IV{7
:^H,
nF`#
q*kSx
COiw
"PI$
6:xZ
xKfI
A+@
zKJxG6"
o+xR
u}Z%:t
!\>S(
;P&A9
hZ00
qajCJZ
hm|.:pb
F!=t
dKh,!
 dP@@O
@7S!
p2C
p)!L#(
$fs1
$'1+V
`63@
2bIV<
d0+cC
Qb|%8
M8FT
HD@H
m0xQ
AVT]
[jK?
Dm]F
mM2%t
5Q$b
C8><
Bfi#
t,0`t
572A
Gy@c
]pkr
8gl!
 NgB(
d6k 
J$Ei
|@6hsTu2
b%:G
aD0
NApq
?D[Q
1VF
1l~c
0I\]
vM|d~
!j.(
AQ_00
|NEgW
H2`<d 
oee
JFH0
#XvG
BYRp[
XHBp
pWKx
ac aY
't$I
qe4j
<OXz'9u
<g+#
zBn
EMLhnD
YcwJ
5||'
 9SCx.[d
SdYU
;%(~SNI
17D-
SDtGj
zLQ5)
I<!'h
*mx=
 L2/Z
UA'&
4<A0q
$F0A4
o&go
B1\ 
I>Ej*4)
*pn+
4(5NZ
'^`#
=xgL
0#Xl|
jy$3
Vqr-
d2H$
rv"++^
kBM,
hf >
*K$0
3e`>B
ibL`
DG"2BCj
bwlf
e,>U8S('
BXLK
F.$S/
Q5@F$
cl?BF
HKxB
YeJL
>Jo@a
19*pH}##
zNLHH
u[%k
138"4Tb#
@/8K
vQ2D
iqLU
RH%)l;
EB~H
<;0<L
_6B0
^xPD
<AkF*
\s@"
Q9i44
bvx2
l@D5j
$]]k
mb3.V
&M:oc
%(X5
\\f.
:\OR
hJD0l
}o8c
FB'J
?i@C
trmZ
PwD!5
t][qK
qjQ<
Iq9k
Z8{J
b<aS
(r.=&
+Czr
]vQj
ZNcxB
@[06 
AM/f
{jqdf
ge65
K16]
[f]
m[[9+
36[]
5lr<
)e4U?z
S!RarcD
<:W'
r8^D
]|!VY
fqF7
Vlva
%AO0$
9jIQ
%_<F
\.i&
B}+t2D
'mvB
cO\}
d?xX_
QI4pO
$>wax
;9X;*`
R"ZOl
Vb*sNn%
}.ys
#JK:
;&264
pjE5HV0t.
#RS#0
7\Yb
q81Q
MqIo
Pp-84j
x&Tl
CG@8
FL*:S
?HmH
L#6C|<
1n)n
?:]j@
!rU~=- ^|
04^|@
.lsg
gq-x
vOR8
N+1L
W<gT
mk#d
{tTv
v:7z
+t V
5{VF7P
e]LD
6yG
<q1f
?X2l
{,Us
k"YL
LCr2
8'eAS
@MUY
<$"2
mq.Lpc
#,;W
/a2[
OPJ&T
3Q~=
$b?
90CD
vqs_
%{b5D
R9as
5PSE
)BwE
-y?5
E>%o"}
QFKV=S{U=
yc!4;I
s^*x
E{r6y
Kzr}
O&WOvx
Il#C
`3,]
.7Fk!D_
f^a"
Qn%*
?RL%
CTGd
@vpf
$[,:
.SQ+
%R[
d}(@$
{5k%
{'3X
DXbB
,q|Y
}H2E
MV|B2
}/oa
'~Sm
rGqb
oIAUn
~d|%a
Ij/.
t|l$
}sJ*a
fs{F<
Nl.C
[p9K
O*2"bf
Cl/M8
MDyY
<k/]
PS9YB
PEi2
O)aJ
~Ru$
{RMoKZF:
)6$)
a.,z<~u-
z*rp{
"[ %
Kjm
I"s@
@2NX
[b74
_ih 
@g"-vy
n$~%;WX@_
ZS+y
)~A:
2$g9|
epd(OHF
QyZD
r0@K|
L-t;I{
mclZ8t
5X)8~
:_u^
wDf-
b=Yj
s)5~}
j(g.qg
\C@)k
#9Ku
XZL;
[,iu(
.0yL]|>C
9>s#Vx
h,ge
'pdH
pJou
aptd
q[(WmZ!
b-/8&
LX[OQj!
!>DF
(",O
V7dT
ci(e
K&w;^
9:GG
7EYV
G&i1m
6"<(
{gv8
_lNJ
8O[Img
xPQV
|k+e
(,gmwf+
/i%I
fKbda
vo3a
JqxI,HS@
4aBs
N'K0
s>3m
Qnak
GT(h
EHG\
!BUQ
t.4u
L(ti
x^'\%
/EfJZ
<O2v
}s]j
m4,K
Yg(D>|g
|{n@
huyy~zZ9
l00z
L)'-
;.vy
FlUm9F
byJTc7
1xt&@
mf\pn
v!nG
Q<<u
gQ"C%(2
4$!I
S9D2
q%mA`
B/I>K=
D@`uk
T]t:
i6`[d
}AY;
bwOo<V
( U`j!
^.n_
S]:#
k%SXU'
2^lE
7LB[#
[I;-
Sbs%
]FCM
,3VHw
p!ZS
Bn=?
6e1]
m:)_
Y!<H
Er&a
aKR!:
.RG|
d0*C
+uTUQ1
lzx/
46T,
HmAI
+W7f
F/y}
tj;A
lvkq
!&w,
.3i7
&]UP
X-]zusF
9k8m
^(w|
Ti41U
4m(axM
~q&a
^-Pe
"UQj
z8vg
9+BG
.7ho
sVbq
"G.H
#TW^
< :mI
Je1H
.6'fA,
7[mr
}UQ<p>
Djht
hb7Q
(8)R
mv2]
6c2;
lZ6%
}+kX
Vb>lP
tbYC
4OvY6Qv
gk],
l]33
)EX?
kGN>
1p>FPB
o3[FC
4;aE
}>,S
Nf]5
XG8N
-e+1
x6XO
>QU%
Cgn'
iJvo
-.@h`.
4sn1
38}J
;!LX`f+
s@T?
tuiy*
[s|$
"a1y
wt1k
/T?u
+,nCj.J0
7m,R
rPQ"L
iLa 
0H#H
(8#vZ
+h%/
BImH
EmEg6
^tR]
b < 
Km>=
:!]kE
W8J7\
&/6%
nQx5
NCo}
3IvC1&
"6xh
CPoS
>su3EoC$
<sT3
'BLm-
Le$w
MZEtiV
j!eC
)[?[
D]r"K
Cd_`o
<N":i
"X6r_{
spQ:_
U{_iJ
QGkC
e]+#3
;p}
DwU4
BB!F"
OKu!
Qsyz
Cb;M
49Rg
B>SO
p_5$
Ly?I~
%uw`
]"M=
@KBE
:S3(
yn!
r^:)
)tQTE
=+{S
N].@`Y9q
fmvv
+^a&G6
QA%(
51I;d
}4a'
vxx|Hn5
NO i
bmQ;X
&ms2B
j+yO
M@ShA
56&
nv0[
-{>9
.H)PC>
1 c^
UVkT
$x%[
zXx~
EN (
3>[/&
Vl9aV
_"oZ=
RZVg
raE[
p8'u
+B>@8
|j3p
MjUd>
tzM
pmO^
\_S&
K/qJ
n-0@
'q.w
>-I`
B4iF
QVJb
`@D
D#wx`@$
<kY<
CT5A
7<X8
`Jl3
4 HP^
lVaA
HM`I
($bIL
r) ~I
*sd5
Z`|E
A$]+
2uJ*
'`{Q
 1D`, Li
5Y0g(y-
/  HJ&
 gYr
'5o+(L
"{?`
@H>kU@
8{b
X@A".NKD
<@&s
,H X4
juAb:
gIdT
)1\1
`])#
\2_Z|
ClY=
WM.{P
9Bw
)&L`
f|$V
;k,@
:T ;1
UAiO
Q>la%
+6a`1
#s{G
^H[C
_"(l
 CL`@BT
q(8mAp
 J\B"
_T:"/
-{# 
g|8A
.QEA
)Dh>
Kt62
})*
XG2n
HDJ!
 (IF
 *(H
+,$Ku
[yqsp\:
0%wV
x*|o
qzRY
r>hP
yT*QY
WLsV
't,!z
!I}d!
 Ls
c(rd/
<D o8!t
-7'Bg1
d'yAC
,`0V
\+`P
t{VK5F
>XU1w
@6j`
pqFf
@{xzS
k6=7
34gJ
6EPF
!\U|
:9av"
vc$t
CBB"
lPLc x
ZbjAH
NI3`
Kn H
fm3h?
)3@ w
&.X&
'i;AP
 yYb/
A`}8#N%
RcCz
4&3
S*2c
E ~Z,T!
+8q9
 F jJ
$2Wa%b
`!!<
4@;qSN
ud.f
apI hh
A$Q#> 
!}k
U1TR
qR9A
dMR<
6p"!w?1oY
SMAEPdM\H!4
FL'
%qJN
6"[H
"jr|.
0'WM$hx
j~4+
XyRCG
Hi*I
U0.GZtb
Yr/r[X
DGwz
@030
%=*^*
#(^*
O@^4
`,Z.
8(P#[
N"4@)d
+`rQ
!sV]
(vMoC7
aa[J
eH[!
$^Xq
MSgX0
p)7
p]-L
Q 0MP
 { h
6y@4AS
UE8e
.ILB
2GL *"7
#+Eb
)nH. E
Vh)L9
D,=+
I\q2T
&X0G
._t[
|6XQ
5g#j
BmHN
Ye@7<IA
sx%L
$>A'#
PhtP
\fBt
B\NB
mZ|)
<)=Q
0l>G
@z$t
!:wM
B%&;
AnF1
sKbh
yyRE
0cD"T
aHzCP
s3fd(P
 Z3}8P
A5iSJm
qp2?
2~*v:
^*d@
Z@n2of
+cpvAF
4d:]
p5ht
0v3`
E_h@
6sD"
'`Z'
.hwzI
`]
w g[
H%B>3
l8;IO
$?"4
SsDqd
$0'P;-8)
l+r<
*cL~
kk"0
BEx{f
w<#]
o%&#
+O{ 
\u)g
;F@K
-c`u
^nc0@)
2_eF
H"pR
z]R@e
UerZ
8.Pg
K2:nB
>)S?1
\()KT4 
tBJ4
: s"
LTEh
ySB/
0.nv
kX(JR
lX1T{
3(P _
|o$ 
b Gd
S~>+W
9.qJB
n@fo
iNpf`9
AXf
+y#,Q
X`X`G`Q
0W"=
H,5NR
(vYhU
)ZJ
)"ox
bFh{
|y|$-
\i,>l
F^ Q
ATJ%NT
Dz`S
|`s8L
cxCU
l=(<Di
Zc- #
j82%
T@8I
|Ed9
@{"*
4HA6
VTjRf
` =x
J6"zK4
!Q!t
.Ax*
@LLM&
@kl6J
* 5N
&Za2
~7B=
IX`^
LjhDU
!Pn$
1ej=
|Pu;
.AFp:$
+l68
pIyP
`]wH
oq"yl/A=j
%y o
G]4m
q\ TSM)@!
G0`
`I1s
2;pdH
Gh,!
!5(|
?1hCj
.jW@
t%+B1
][&M
]b.=
P:&a;OS
.V|/
IKPi
H)<{
p2E6i
BXcp
peha
 D#gz
6TxzA4T
0p[pT'
H)UL
2gDdc;>
>R^V
'!>
[1
y$EP5
1461
9340C
@p(F
bvx2
yup0
(w''cv
lz$|
W'>{
O8C(
-&i!i
$@Gm8i
b`m_
<|5l^
v1I>
G,N3
1:q-
wsJ_
7wi,
Pjg0
K`>z
rO-B
<uEPPf
0 V}
f"%p
jQ6[
O8C(
E,uT
xY1FB
E,uT
xY1FB
E,uT
xY1FB
Lj9m
iY.=
eDP_
P0(O"
F!py
Nej8&
%r}M6
\-V;:
B%KG
pANp
/=vv4BbD9
S10=
`&/dk
IOwD
kK_A
*rLU
Zvw1)q
eB,.2c
z_ q
i2`0r
DRLD
y-4z
k4Rz3
fR?;
q.z*
OG`Qgo
wXq]SQ
c9Pq"
_XM!q
Q4PDPR2
;#Bt
M|j#
F4+.F
w[qD
^IK)
?4i&
;lH 
cS\)
ZK`s'P
E/M9
#-5Fb4^dWd
BWS#
/p6|
2J-Q/
B"(W
v|/>
lkb1
EOL]
ABXx
z]E?
sqG5
%}tH
Nua<
.U3i
MVPs
>K*S`
NR#u,
6/N=A
Xatyx
<"28
3pHjN
EJj:
J8{'
^ 3(HE
6T{A
Bt:
 Ab1g
rn4$
E|GG
S4OM
a:&}
h7iI
K04h
SR+SRU#
Q-OI
2_E`
sn}5
hFTU
2Rmm
$[3U
ehI#
aRbiC
>YzR
jQu[
eS?R
V=4}%=B
humg^
Ah8)
(+L
|.sf`
RP?jW
1j*&D
ca{G3uG
1S3f
^W.9f
3[!|
~sk(
U.xZo
<'Rb
=V4$B
;$q~
[)cZ
_W(m
VVHv$
^0Zek
S[>T<>
]:J4:4*4
 G?F=F;F7
8:qTb4
}f~F>
z5WX
3WXcs
[m!6
+~G0"
,dH$f
<yR</
;!rs
1:l@
9sf6/
Tmcz
{o\"
mg[^
NJ!jyhA
Cbh+<
GsS?a
h|./X
X:&|
Wol&
p8lt8
szc0
pX':
0Nt8
a2-2
`2-r
Lk$K
Hlg|
OOu6J
+wiA
Xigz
d/^5
I@KH
W:lz
;]1/
pYW@-~
dNgbZ|
\|-i
t86]
HzM >
OIct=
6]|
3ztd
+]Kc
Ezj5v
4qhs
~=t~
<DKc0c
d"iO
qCk/^
)G"i
hAJ'*
adLD
cIv"i,D
a1h8I
$[b<D
dXBJ
%C2,!#cY
%8&s
aN/2
8![j(
?8I:&[X
g}mPi
D"M
0O"<L2
a#"\
StX|J
1A7[N
4I$5
3g}F
kL/H
3gqF
@bcb
5FDc/
q^;f
K-QA
!5E$
pL|D)
O$fv#
Z3zBmf
^,*|H
J%rqV
/:pc9
=?kZ
Ir/<`n
'iSr
!1n{1
, |!e
rQJg
0}X)
kHwi
W!z&
a<eQ
+r"40
TYz=
C/l~
&}n6
Ez,u
hPQ|
brka
E\Su
R8(Fn
[o]_}
ZWH%
vetr2
gX(P.
]xfU
EWm
O.-[
sN$|&
t(Wns
YdW6
6\JC
Mz,H
]6M9
\Y8<
KwA0
;ihIZ
Gwoq
#`Xg
_Zal
i3#wQ
9Akv
/cd?/
[P2r
-_3GK
%Hi
3~kO
5%a}
z(e+
sjW
%-9[J
%zCw
7tW;Do
!zCw
7tW;Do
!zCw
7tW;Do
!zCw
7tW;Do
$zCw
7tW;Do
!zCw
7tW;Do
Byk8~
QM('?
:""o
&S4q
EeVP
0(?C
olA%4
7 gC
#A~F
pQ<\s
O+v,
{"QJRB|
TdOHF
8?(q
I:Qu 
SM@E
-E'v
DK-z
YL0F
@Ag3
5v%AI?R^R"#-
$$$S(!##
G!et
0 U6w
E2ch0
wDXqf
WPEuI|$
rsnJ
2dk:
gwZt`
DhSB
LF^&#/
LF^&#/
LF^&#{
LF^&#/
LF^&#/
LF^&#/
LBZ&#/
LF^&#/
LF^&#/
@J&d
,[~q%
@0}rdW
'%SKz
6Ip[
j{82
G>m k7
2Nlw
qt1~
M#4Dd9
r)eF
y{xGq
fjX]
[7 
NkbK
h,@G)3
vBYp
|B1a\
9_V2%
2'F:
> sTS
-Q+nB^x
t%%
2G5Y
,>Bf|
!3>E
z/]G
T7ZU
Z3fGe
Tv)+
hi#H
2G5Q
S,6A?
lY-[
]5Aq
)khUW
Hqrq
Ruj8
{EpU1
uB-O8
vt]X
b5V>
<EO}B
$Pvt
oGW,
;k43
+pBe
l,c'
{i>-
dCX<F
;Cfh
8-00.\(
8KdH
.BYS!
LUj
8G'd
l:Ng
#d%Q
c,.'
PDJe
)';
|uW^
u>Z)E
xTOf
6joFOBUYKR
I`W,
1/}i
;pi:3
y~xxN
&,VQoL
cBy;
=64?
zS,#2
51b'
0.<p&Zna
|=H:
H/C'&
7BHf
n1GS
/V=f
-Fy4
wX"D
Yd1"
/~J!
h%{8
7t-#h
s:=P)i
g!AM
!QP
<MG
%^8&
Jgr2&
{egt
|yS
Q1bTQqV
q_Ag
tWb:
CSIkPl%}
A\d5&j2j
j(j,R
F-5
"fP#>
0Yj1
F(5~
nRKX
N?M.
fL n
W7(?
8&Eja
!|s?
;w>*
3!U6)8
q0bO
_"44
4{&~
NH 3`
Rj8v
OO@R
p\yF
RBXp[&
vrO\@
0pSl
8q,]
s&$G5
.Ku=
1JP]
6-@>*
@7cj(
Eh~#
P"5D
h@H[
q=di
c#ct
@[DY"
4qOR
<2~
bvx$
#tvxz|~
#tvxz|~
 @(=
(@(=
0@(=
8@(=
@@(=
H@(=
P@(=
X@(=
`@(=
h@(=
p@(=
x@(=
 @p;
(@p;
0@p;
8@p;
@@p;
H@p;
P@p;
X@p;
`@p;
 ???
 ???
 ???
 ???
 ???
 ???
 ???
 ???
%
%
%
%
0Mk4
VX!8
VX!8
0Mk4
VX!8
VX!8
0Mk4
VX!8
VX!8
((d
((d
((d
!"33DDD
3CDUUfff
dddddddd
????
????
????
????
????
????
????
????
????
????
????
????
13DDUUU
????
????
4#1"#5
Su&E4cC
????
????
????
????
????
????
????
????
????
????
????
????
????
????
????
????
????
 @ `
m
@DDD
????
wwww
wwwW
ffff
fffV
????
UfUU
UeUU
UUUU
????
????
????
????
????
????
????
U3UU
U5UU
????
????
????
????
tsrtlaesgfCfgfCdvppagfCektpafpcdpcdefoicfumtMEPLHTLdCIBd1wfi2wfiwfengfCtgfC0tbplLCDAFSPdSALrCALiTSRP1rdf2rdfgfCbLACUSCIBlCDxHTLcACmHrcSdgfCLCIBTlCmHgfCjgfCcgfCm
iBoot-13822.0.166.0.1



????p
00hCW1hCW
tbuarpua
(((((
AAAAAA
BBBBBB
%(null)
<<WIDE CHAR>>
<<WIDE STRING>>
<<N SPECIFIER>>
<<FLOATING POINT>>
<<PTR>>
lopl3D3287DE-280D-4619-AAAB-D97469CA9C71
nvram
2nvram
common
2common
system
2system
0ghc1ghcFtab0tab1tabPylgdqilogol0wpl1wplmcer1glr2glroglr
KNUF
KNUF
SOCD
AF2A
BAD MAGIC!
aidi
rvcR
rkosftab
sik-
0K1'0%
Apple Secure Boot Root CA - G61
Apple Inc.1
190821182918Z
440821182918Z0K1'0%
Apple Secure Boot Root CA - G61
Apple Inc.1
N2|sn
n/wyo2
xVU1
aXYY
B0@0
gN5 
Fydy
QgQ$"
i2[y
Fe,.o
:{Hc
g^\o
manx1
BNCH
ECID
lpnh
ronh
rpnh
snon
snuf
srvn
0K1'0%
Apple Secure Boot Root CA - G21
Apple Inc.1
141219201310Z
341214201310Z0K1'0%
Apple Secure Boot Root CA - G21
Apple Inc.1
I-ix_'
_l`.q_
:aR
w[\J
BW=B
e\y4
+?Vh
B0@0
P(e
P2ff?
0S1'0%
Basic Attestation User Root CA1
Apple Inc.1
California0
170419214156Z
320322000000Z0S1'0%
Basic Attestation User Root CA1
Apple Inc.1
California0v0
SOI2
v3]D
B0@0
0O1+0)
"Apple X86 Secure Boot Root CA - G11
Apple Inc.1
170322214243Z
170323214243Z0O1+0)
"Apple X86 Secure Boot Root CA - G11
Apple Inc.1
\q(8
w&]Be
MXt?
`/-E7
@=%B
!."{
C \x=>
5bbc
B0@0
Fh[;
s#NU
p%OR&
comb
trst
rvok
fdrd
secb
trpk
IM4C
CRTP
mcmb
YAPS
ATEM
INAM
PTRC
KBUP

HpJX
D7q/;M
}Uo
+Yo,
&\8!
* qW
LwH'
L*~e
}Ut]
$o,
vRQ>
8STs
LwH'
|6*)
g&3g
[
"
D7q/;M
}Uo
+Yo,
&\8!
* qW
LwH'
L*~e
 uuid
,hmh
!L"M
)F H
QCCC[
%FMC[
C##C
&M,@
#@%@[
#M+@#MkC
C33C
#@&@
FUUUU3333
pGpGpGpGpGpGpGpGpGpG
K`Mh
M`Mh
6xI$"0F
bH88
`\JPihI
Pa\H
aPaoI
mH@"
ctcPI
bHh CH`
 UIfJ
eM(F
`^H0`Hh CH`
GXH)F
VH ")F
GUH)F
%GI(F
GHHI
!GJHN(F
%GJ F)F
GFJ(F)F
`Hh CH`
P9HPa9H
JS\[
F80J1
T CJ
RBN0|
V @M(Z
)FV1J
Z!hZ
Z+F(FZ3Y
-neCDn
5FJ5,[
%Fue(F!F*F#F
o!F*F#F
J!F#F
F!F*F#F
P 4I
!!lT)F!1"
ax "
TJ!qZi
h!a\I
2FJ2QZ
Rtml`
1F3F
F1F"F3F
Bija
 F)F
q`@pq
 M(h L!k
`pGt
"1M1H
X%N0@
,  N2T
(|)!pT0k
% 4T
$ 4T
!!TV 
R!F&1
`0  \
!F(1
, "\
X QI
EHEL
0 (\
pD@4q0 (\@
$ (\
* (\
Lb\R
6N0F00
$1M#F
@)B!F1
@+B&
" F1F
C)Fo1
 F00
H@"1F
!hP x
!hT0F68
hP0F28
!hP0F.8
!hR0F
!hP0F
!hP0F>8
g0F:8
g0F8
!hT0
!hP0
!hPP682
)T< )T
hdHHpb1t
0`}!
.FT60F!F
(F^0!F
"2u%
@sx&F
F84cUS
GM(`
h`0FT0!F
^6!F
"0F&F
 F00
F6<$h
%\QF2<$h
%\QF.<$
%\SF
%\QF
%\QF<%
%\Q
%\QF><$h
gF:<$h
%\QP182
((((((((((((("
&@$1F"F
H1F"F
 `4
J %I
RT $K
 F)F+F
#IACH
Fx3!H
 X)F2F+F
82P1
F o@
pc\Ux-
yDy$
xEx-
0``z0r
rX]ax
H 'J
%%J+F
!!T@  \
@40z
D!a\
 FD0
p@  \
@4pz
DB(D>w<-JF%5*/
c@8HLDccccccccccc]<QYU
x " 
\%Fd=
N UI
RTM,F 4!F
*F(2
C(F=0
hTH!l\&JSm
a1bsb
b,F1F%
bKb`l
0F 0
bpjHb
b`l@
? !\
"J@"T
1F41 F
4%p]@
IX1 "
!A@qU
JS\[
 0Xr
&f`n
P%aH
CDPBX*B
JS\[
#C`S
@(F!F
"FBC
h&hLi
6Na[
#IIh
)F J
a0y@
&i i
1!a3`
!q`R
"q1v
a@h b
$#CC
F 4'%
$!AC
.F 6
L%h)
H(@ `
RTKSTACK
 ApplePMUFirmware-608.0.7~97.release
 uuid
8RJ<@
,hmh
!L"M
)F H
pGpGpGpGpGpGpGpGpGpG
u`uh
u`uh
X`0h
`0h}J
qM +
aN0ibJ
C0a0igJ
0api
5`I$"(F
NH88
`piQI
paHH
`YMYH
`(aYI
`haXH
WH@"
cLc<J
bPh CP`
 >INJ
"(F1F
GKH1F
IH "1F
GHH1F
%:I(F
:H:I
!:J:N(F
%9J F)F
G8J(F)F
`Hh CH`2HPa
 M(h L
`pGt
"1M1H
X%N0@
(  N2T
(|%!pT
! 4T
  4T
' "L
%%TV !I
R!F"1
a,  \
!F$1
(  \
vM`"F02Ja
X RI
, (\
pD@4q, (\@
  (\
& (\
E"I
JS\[
L(F!F
 x`p
!A@!p
 0Xr
&f`n
P%aH
CDPBX*B
JS\[
#C`S
h&hi
api@i
#IIh
)F J
 qiJi
piAhpi
$#CC
F 4'%
$!AC
.F 6
L%h)
H(@ `
RTKSTACK
 ApplePMUFirmware-608.0.7~97.release
%llx:%d
/lpol
iboot-path
diags-path
boot-device
%s%c
/boot
nvme_firmware0
nor0
ibd-path
%s/%s
nvram-migrate
one-time-boot-command
field-diags
Permission Denied
Memory image not valid
multiboot_init failed: %d
local policy eval failed: %d
iBootData
ACIO
RTBuddy
boot-breadcrumbs
WCHF
RestoreANS
auto-boot
boot-args
debug-uarts
apt-carveout-size-mb
auto-boot-once
backlight-level
backlight-nits
boot-command
com.apple.System.boot-nonce
ramrod-kickstart-aces
stress-rack
StartupMute
StartupMuteAccessibility
SystemAudioVolumeExtension
SystemAudioVolumeSaved
mute-next-boot
dev-unset-debug-enabled
idle-off
is-tethered
darkboot
force-upgrade-fail
ota-breadcrumbs
failboot-breadcrumbs
recovery-breadcrumbs
com.apple.System.tz0-size
com.apple.System.rtc-offset
upgrade-retry
preserve-debuggability
upgrade-fallback-boot-command
boot-volume
alt-boot-volume
policy-nonce-digests
boop-storage-nonces
iboot-failure-reason
iboot-failure-reason-str
iboot-failure-volume
boot-image
recovery-boot-mode
recovery-reason
system-passcode-lock-blob
prev-lang:kbd
panicmedic
panicmedic-timestamps
panicmedic-auxkc-present
panicmedic-telemetry
panicmedic-engaged
iboot1-precommitted
sfr-manifest-hash
recover-system
recover
system-passcode-locked
Entering %s recovery mode, starting command prompt
iBootStage1
command
idleoff
en-AU
en-GB
es-419
fr-CA
pt-PT
zh-Hans:
zh-HK
zh-Hant
Remote
Local
Microkernel 
=======================================
:: %s%s%s
%s boot, Board 0x%x (%s%s)/Rev 0x%x
BUILD_TAG: %s
UUID: %s
BUILD_STYLE: %s
USB_SERIAL_NUMBER: %s
=======================================
bootdelay
true
aborting autoboot due to %s
remote boot
user intervention
fsboot
fsboot-safe
upgrade
post-upgrade
recover-fallback
kcgen
field-diags-paired
iboot
reboot
reset
bgcolor
setpicture
usage:
filesize
filesize variable invalid or not set, aborting
Warning: Disabled hibernation for this boot due to side loading firmware
firmware
devicetree
getenv
saveenv
setenv
setenvnp
clearenvp
lpolrestore
picture too large, size:%zu
Memory image corrupt
flipbook(1): image_offset=%d image_count=%d
flipbook(2): image_offset=%d image_count=%d
setpicture optmask [<addr> | *] [<size> | *] [<image_offset> <image_count>]
setpicture optmask [image_4cc] [<image_offset> <image_count>]
setpicture optmask <file path> [image_4cc]
optmask: 0xVVVV00TM: VVVV=voffset override, T=operation type, M=operation mask
VVVV = vertical offset in pixels (0=no offset=iBoot default behavior)
T = 0x0: load image from memory: addr/size
T = 0x1: search/load image in all block dev with 'type'
T = 0x2: load image from 'file path', match all type without type specified
M = 0x1: update the image on screen
M = 0x2: don't clear previous images
M = 0x4: restore mode image
M = 0x8: select flipbook image with image_offset/image_count, T!=2
sera
simetra
build-style
build-version
config_board
board-rev
loadaddr
ramdisk-size
/usr/standalone/firmware/FUD/iBootData.img4
false
/usr/standalone/firmware/iBoot.img4
boot-stage
failed to execute upgrade command from iBoot
failed to
load upgrade local policy
scan SPs
mount upgrade partition
/boot/iBEC
load upgrade iBEC
%s %s
ota-result
ota-failure-reason
acio-cpu%d/iop-acio%d-nub
__TEXT
__DATA
ANS2 afc_aiu_ans_dual tunables
ANS2 command_accelerator tunables
ANS2 storage_lane_common tunables
ANS2 ccu_security_interrupt tunables
arm-io
NFMT=
quiesced
ANS2
ans/iop-ans-nub
DCS Init     REG(%p) <- 0x%08X  (Single-Reg-Write)
%llx:%d: spin loop timeout
DCS Init     REG(%p) <- 0x%08X (x%d)
DCS Init     REG(%p) Poll: Mask (0x%08X) == 0x%08x ...
 [data==0x%02x] 
DCS Init     MRCMD(0x%08X)[%dx chan][%dx rank]
 SEND: 
->%d%d 
 POLL: 
%d%d! 
density_to_Gb
unknown LPDDR4 density %d
dcs_get_memory_size
DCS Memory Size grab has jumped the gun
dcs_get_memory_info
failed to read vendor-id/config-id, vid:%08x, config:%08x, rev:%08x
sdram vendor id:0x%02x rev id:0x%02x rev id2:0x%02x
sdram config: width %d/%d Gbit/type %d
sdram config: bytemode %d 
unsupported DRAM density: %d Gbit  (with Chan==%d x Rank==%d yields %d MB)
dev_density: 0x%x
Memory_size: %llu bytes
DCS Init Lib Built for: iBoot SOC
DCS Init [%s Build] for ChipID==0x%04x chipRev %c%c (#DCS==%d, #Rank==%d) [%s] using %s
iBoot
Coldboot
UpgradeInit
??BootType??
DCS Init     Calibrating Command and Address...
DCS Init     Calibrating Write Level...
DCS Init     Calibrating Read DQ...
DCS Init     Calibrating Write DQ...
DCS Init     dcs_init_op_set_cal_cfg() freq_bin =%d, RL=0x%x, WL=0x%x
DCS Init     Calbration Config params init arg1=0x%x, arg2=0x%x, arg3=0x%x
dcs_init_process_condnl_block() called with condn_flag=0x%x dcs_init_loop_cnt=%d
dcs_init_op_if_density_id() called with density_id=0x%x, condn=0x%x, arg3=0x%x
dcs_init_op_if_density_id
Unsupported DCS_CONDN passed to dcs_init_op_if_density_id()!!
dcs_init_op_if_bytemode() called with bytemode=0x%x, condn=0x%x, arg3=0x%x
dcs_init_op_if_bytemode
Unsupported DCS_CONDN passed to dcs_init_op_if_bytemode()!!
dcs_init_op_if_rev_id() called with rev_id=0x%x, rev_id2=0x%x, condn=0x%x
dcs_init_op_if_rev_id
Unsupported DCS_CONDN passed to dcs_init_op_if_rev_id()!!
dcs_init_op_if_rev_id1() called with rev_id1=0x%x, condn=0x%x
, arg3=0x%x
dcs_init_op_if_rev_id1
Unsupported DCS_CONDN passed to dcs_init_op_if_rev_id1()!!
dcs_init_op_if_rev_id2() called with rev_id1=0x%x, condn=0x%x
, arg3=0x%x
dcs_init_op_if_rev_id2
Unsupported DCS_CONDN passed to dcs_init_op_if_rev_id2()!!
DCS Init     dcs_init_op_if_vendor_id() called with vendor_id=0x%x, condn=0x%x, arg3=0x%x
dcs_init_op_if_vendor_id
Unsupported DCS_CONDN passed to dcs_init_op_if_vendor_id()!!
DCS Init     dcs_init_op_if_other_id() called with other_id=0x%x, condn=0x%x, arg3=0x%x
dcs_init_op_if_other_id
Unsupported other_id passed to dcs_init_op_if_other_id()!!
Unsupported DCS_CONDN passed to dcs_init_op_if_other_id()!!
DCS Init     dcs_init_op_if_other_id() result:%x
########### Step %d. ############
DCS Init     dcs_opcode_if_num_channel() called with num_chnl=0x%x, condn=0x%x, arg3=0x%x
dcs_opcode_if_num_channel
Unsupported DCS_CONDN passed to dcs_opcode_if_num_channel()!!
DCS Init     dcs_opcode_if_soc_rev() called with soc_rev=0x%x, condn=0x%x, arg3=0x%x
dcs_init_op_if_soc_rev
dcs_init
DCS Init Config is NOT RIGHT
 DCS Op Descriptor table size grater than DCS op table size
Unsupported binning_type %u
ASSERT (%llx:%d)
tunables not found for chip revision %x
Invalid tunables struct
%02x
IMG4
IM4P
image4_callbacks.validity_cb is NULL
image %p: bdev %p type %c%c%c%c offset 0x%llx len 0x%x
linked list item %p corrupted
Unknown ASN1 type %llu
%c%c%c%c
%s/%s.recovery.img4
%s/%s.img4
%02X
target_oracle_update_contract_from_cookie
unknown cookie (%u) for target
ASSERT (%s:%d)
build/macosx/j313-iBootStage1-RELEASE/lib/oracle/include/lib/oracle/_product_queries.h
_oracle__Q_AUDIO_HANDOFF_SUPPORTED
%%%.2X
recover-once
device-recovery
alamo
rtos
phleet
diags
checkerboard
repair
rtkitos
restore_rtkitos
diags_rtkitos
invalid
Apple Mobile Device (Recovery Mode)
Apple Mobile Device (DFU Mode)
SDOM:%02X CPID:%04X CPRV:%02X CPFM:%02X SCEP:%02X BDID:%02X ECID:%016llX IBFL:%02X
 SRNM:[
 NONC:
 SNON:
 DCC:%X:%X:%X
dcp-auto-boot
spi_nand0
system-volume-auth-blob
======== Start of %s serial output. ========
root rpc task
Paired Recovery
System Recovery
iBoot Recovery
nand_syscfg
nvme_syscfg0
nor_syscfg
com.apple.os.update-
BootPreference
tcon-device-id
Tonga A1 is deprecated
secure-boot
debug-soc
enable-auth-debug
GRAPHICS
SIO_DMA
SISP
DISP
DISPEXT
ACC0
ACC1
DispTarget522
nvme_nand0
apciec0
acio0
apciec1
acio1
kis-rsm
[iocv=
dart-dcp
dart-disp0
dart-dcpext
dart-dispext0
usb0
usb1
admac
disp0
disp-ext0
dcp-ext
dispdfr
scaler
jpeg0
jpeg1
apcie0
apcie1
apcie2
dispext
nub_spmi
smc_out
smc_in
arm-io/i2c0
arm-io/i2c1
arm-io/i2c2
arm-io/i2c3
arm-io/i2c4
nvram
nor0_raw
nvram_raw
dart-ane
ANE.img4
aop/iop-aop-nub
dart-aop
AOP.img4
dart-ave
AVE.img4
gfx-asc/iop-gfx-nub
gfx-asc
GFX.img4
dart-isp
ISP.img4
sio/iop-sio-nub
dart-sio
SIO.img4
pmp/iop-pmp-nub
PMP.img4
reconfig-breakpoints
No reconfig data
Unsupported mode (%d) 
Unsupported number of channels %d 
PACC.cpm_MTR1.MTR0
PACC.cpm_MTR1.MTR1
PACC.cpm_MTR2.MTR2
EACC.cpm_MTR1.MTR0
AGX_MTR_TOP
MTR_TOP_SOC
MTR_TOP_ANE
MTR_TOP_ISP_CPU
DCS Init          Spin %d us %s
Changing Frequency to Bin %d
dcs_change_freq
Unsupported Frequency Bin Request: %d
DCS only handles 32 bit tunable size
DCS0 Tunables MCU
DCS1 Tunables MCU
DCS2 Tunables MCU
DCS3 Tunables MCU
DCS4 Tunables MCU
DCS5 Tunables MCU
DCS6 Tunables MCU
DCS7 Tunables MCU
AMCC Tunables
AMCNP Tunables
DCS Init DONE!!
calibrate_ca
Memory CA calibration: Unable to find any Vref which did not panic for channel %d
calibrate_rddq
Memory RDDQ calibration: Unable to find any Vref which did not panic for channel %d
amp_opt_center_add
Error! Non-sensical (hi < lo) in calculating center [hi==%d, lo==%d]
cacal_find_right_failing_point
Memory CA calibration: Unable to find right side failing point for channel %d
cacal_find_right_passing_point
Memory CA calibration: Unable to find passing point for all bits on the right side 
cacal_find_left_failing_point
Memory CS calibration: Unable to find failing point for all bits on the left side
Memory CA calibration: SDLL ran out of taps when trying to find left side failing point
cacal_find_left_passing_point
Memory CA calibration: Unable to find passing point for all bits on the left side 
find_center_of_eye
Memory calibration: find_center_of_eye: Left value (%d) is > right value (%d) 
wrlvlcal_push_to_0s_region
Memory Wrlvl calibration: CAWRLVL sdll reached max tap value, yet all bytes not all 0s
Memory Wrlvl calibration: DQ%d sdll reached max tap value, yet all bytes not all 0s
wrlvlcal_find_0to1_transition
Memory Wrlvl calibration: DQ%d sdll reached max tap value, yet all bytes not all 1s
wrlvlcal_find_1to0_transition
Memory Wrlvl calibration: max tap value reached, yet all bytes not back to 0s
wrdqcal_sequence
Memory WRDQ calibration: Unable to find any Vref which did not panic for channel %d
find_wrdq_center_of_eye
Memory calibration: find_wrdq_center_of_eye: Left value (0x%x) is < right value (0x%x) 
%sINTSTS(%d): 0x%08x 
AMCC NONPLANE error: %s
AMCC PLANE%d error: INTSTS 0x%016llx AFERRLOG0/1/2/3 0x%08x/0x%08x/0x%08x/0x%08x ADDR %#llx CMD/SIZE/TYPE %#x/%#x/%#x AID/TID %#x/%#x
AMC PLANE%d error: AMCC_IRERRDBG_INTSTS = 0x%llx
Unhandled AMCC interrupt
DCS CHANNEL [%d, %d] error: INTSTS 0x%08x
DCS CHANNEL[%d %d]: INTSTS 0x%08x 
Unhandled DCS interrupt: %s
Storage task
nvme_nand%d
nvme_firmware%d
nvme_syscfg%d
nvme_efface%d
paniclog
nvme_bis
nvme_me
nvme_ean
usb_drd
======== End of %s serial output. ========
%llx:%d
 fp 
 lr 
 fault at 
Stacktrace:
--> 0x%016lx
user
double panic in 
iBoot Panic: %s: 
Board: %s%s:%#x 
Chip: %04x:%#02x 
Build: %s:%s
UUID: %s
force-research-policy
NUB_SPMI
NUB_SPMI1
AOP_SPMI0
AOP_SPMI1
PMU-Main
PMU-Aux
Stockholm-0
color-accuracy-index
bics-param-set
arm-io/dcp/iop-dcp-nub
DCP.img4
DispTargetZ551
DCPEXT
arm-io/dcpext/iop-dcpext-nub
DispTargetEXT
display-color-space
ARGB8101010
display-timing
raw-panel-serial-number
coverglass-serial-number
j293
j313
j456
j274
j457
invalid AES key size
AES: bad arguments
image-version
cmd-results
usb rpc task
usb cmd task
usb req
vbus poll
usb vbus
usb-hi-current
usb-no-current
rsm-usb
root-live-fs
root
apfs
**********ABCDEF
SMMU
DART
disp0-service
dcpexpert-service
audio-features
%s-%s
EXCLAVES
NULL
RGB888
display-rotation
%s%s
GAPF-
wchf image too large - 0x%zx
smc/iop-smc-nub
**********abcdef
Response Queue Empty
Header mismatch
SPMI panic, controller name %s, queue index %d:  
name
syscfg/
syscfg-mismatch/
macaddr/
zeroes/
string/
hex/
Device Tree too large
Device Tree image not valid
gestalt-variant-id
com.apple.System.
battery-health
fm-account-masked
fm-activation-locked
fm-spkeys
fm-spstatus
ownership-warning
%#lx
GFX: Unsupported ASC ID (%llu) or NUM_ASCS (%llu)
amcc
ctrr-a
ctrr-b
ctrr-c
tz2-sram
tz2-sram-ctrl
ctrr-d
panic-region0
panic-region1
panic-region2
tz3-sram
lower-limit
upper-limit
enable
lock
write-disable
dsid-force-enable
broadcast
cache-status
master-lock
boot-image-last
AssertMacros: %s, %s file: %s, line: %d
ret == 0 
lib/hibernation/hibernation.c
sep_client_resume_hmac1() == 0 
sep_client_setup_hibernate() == 0 
Hibernate MKEY
hibernate_hardware_hmac_buffer(KEY_IBOOT, input, input_len, output, sizeof(output))
failed to compute %s MKEY
resume
clean
hibernation image handoff is invalid
hmacsha_setup(0, &config) == 0 
hmacsha_do_hmac(0, (const uint8_t *)input, input_len, output, output_len) == 0 
pmu_get_continuous_time_offset(&rtc_offset) == 0 
pmu_set_continuous_time_offset(rtc_offset) == 0 
%s failed
hibernate_compute_clock_offset_internal
iboot-data
iboot_ifwX
iboot_nefw
Unknown
object size check failed (%zu < %zu)
rsize check failed (%zu < %zu)
size check failed (%zu < %zu)
<<FORMAT BUG! 
diuoxXncs%pfFeEgGaA
********
**********abcdef
**********
unreachable condition
%02X%02X%02X%02X-%02X%02X-%02X%02X-%02X%02X-%02X%02X%02X%02X%02X%02X
%s/%s/LocalPolicy
%s:%s:%s
%s/%s/boot/%s
%s/%s/boot/active
/recovery
nvram-bank-size
nvram-bank-count
nvram-current-bank
nvram-proxy-data
wwwwwwwwwwww
guid-var-trans
nvram-raw
nvram,nor
compatible
%s %s = "
%%%02X
VMiBoot 
LZFSE   
iBootIm
paniclog: prepare regions result %d
paniclog: buffer (addr %p, utilized: %#zx)
paniclog: save regions result %d
-no_panic_dialog
allow-embedded-pm
%llx
panicmedic-panic-count
EFI PART
Update
Unsupported number of FTAB entries
__TEXT2
__ETEXT
__EDATA
__OS_LOG
__SHARED
%s: handle create failed
socd_init
%s: load failed
%s: create failed
socd_push_init
socd push section allocation failed
failed to initialize socd push buffer
Failed to write socd dram address smc key, result %#x
Failed to write socd dram size smc key, result %#x
failed to query the size of the SOCD region
failed to get socd push buffer base address
failed to get socd push buffer size
 @0x%016lx
manifest-entitlements
aptk
seal
trst
%08X-%016llX
early
main
interrupt
Apple Inc.
FS task
usb_serial
Apple USB Serial Interface
%x(%x) 
BOOT
DONE
COMMIT
<%s> 
%s%s%s
Default
panic_trace
arm-io/cpu-debug-interface
panic-trace-mode
management
%s: unsupported version [0x%x -> 0x%x], expected 0x%x
syslog
crashlog
%s :%s
Unknown exception
- with nested
%s(%lu)
/%03d
 INBOX UNDERFLOW
 INBOX OVERFLOW
 OUTBOX UNDERFLOW
 OUTBOX OVERFLOW
user%02u
unknown%02u
kdebug
ioreporting
remotefs
forwarded-crashlog
core-analytics
oslog
tracekit
ACIPC-perf
timesync
entropy
  pc=%#010x  fsr=%#010x (%s)  far=%#010x 
far_physical=%#018llx 
  pc=0x%08x 
%s %u
Alignment fault
Instruction cache maintenance fault
Translation table walk synchronous external abort (1st level)
Translation table walk synchronous external abort (2nd level)
Translation table walk synchronous parity error (1st level)
Translation table walk synchronous parity error (2nd level)
Translation fault (section)
Translation fault (page)
Access Flag fault (section)
Access Flag fault (page)
Domain fault (section)
Domain fault (page)
Permission fault (section)
Permission fault (page)
Debug event
Synchronous external abort
TLB conflict abort
Implementation defined (Lockdown)
Implementation defined (Coprocessor abort)
Memory access synchronous parity error
Asynchronous external abort
Memory access asynchronous parity error
Translation fault level
Access flag fault level
Permission fault level
Synchronous external abort on translation table walk level
Synchronous parity error on memory access on translation table walk level
Domain fault level
Synchronous parity error on memory access
Asynchronous parity error on memory access
  pc=%#018llx  Exception class=%#04x (%s), IL=%u, iss=%#x far=%#018llx 
  pc=%#018llx 
Unknown reason
UNDEF exception
Unhandled SWI
PREFETCH ABORT
DATA ABORT
SERROR
PANIC
NMI FIQ
forced log msg
forced crash msg
Stack guard fault
Mailbox error
Recoverable PANIC
binlog
%s the refcon arg was NULL
DERReturn _DERDecodeSysConfig3ParseAndPopulateMetadataSequence(DERDecodedInfo *__single, _Bool *__single, void *__single)
Unable to allocate sysconfig3vX object. Likely due to an unsupported magic: 0x%X or version: 0x%X
Unable to decode payload with error %d
Tag was not the expected Payload tag %llX
Decode sequence error: %d
Unable to decode metadata with error %d
Tag was not the expected metadata tag %llX
Unable to decode manifest with error %d
Tag was not the expected manifest tag %llX
Decode manifest sequence error: %d
%s key was invalid
struct __SysConfig3PayloadEntry *__single_allocSysConfig3PayloadEntryInternal(uint32_t, const char *__single __terminated_by(0), const uint8_t *__single __counted_by(dataLength), uint32_t, const char *__single __terminated_by(0), _Bool)
%s one of data, string, or deleted must be provided
%s data, string and deleted are mutually exclusive. Caller provided parameters [%s%s%s ]
 data
 string
 deleted
%s data was 0 length
%s invalid arguments. Either entry or data was NULL
_Bool sysConfig3PayloadEntryGetData(struct __SysConfig3PayloadEntry *__single, SysConfigData *__single)
%s key must be non-NULL
struct __SysConfig3MetadataEntry *__singleallocSysConfig3MetadataEntry(const char *__single __terminated_by(0), const uint8_t *__single __counted_by(dataLength), uint32_t, const char *__single __terminated_by(0))
%s data and string are mutually exclusive
%s either data or string must be non-null
%s data cannot be length zero
%s one of the hmacs were NULL. payload ptr: %p metdata ptr: %p full ptr: %p
struct __SysConfig3ManifestEntry *__singleallocSysConfig3Manifest(SysConfig3Version, const SysConfigData *__single, const SysConfigData *__single, const SysConfigData *__single)
%s Unsupported sysconfig version: 0x%X
_Bool _sysConfig3HMACsAreEqual(SysConfig3Version, const SysConfigData *__single, const SysConfigData *__single)
%s invalid magic provided 0x%08X
struct __SysConfig3 *__singleallocSysConfig3(SysConfig3Magic, SysConfig3Version)
%s invalid version provided 0x%08X
%s invalid argument. sysconfig cannot be NULL
struct __SysConfig3PayloadEntry *__singlesysConfig3GetPayloadEntryForKey(struct __SysConfig3 *__single, uint32_t)
_Bool _sysConfig3DeletePayloadEntryForKey(struct __SysConfig3 *__single, uint32_t)
%s Failed to validate HMAC.
%s invalid argument. sysconfig and key cannot be NULL
_Bool _sysConfig3DeleteMetadataEntryForKey(struct __SysConfig3 *__single, const char *__single __terminated_by(0))
%s invalid argument. sysconfig and etnry cannot be NULL
_Bool _sysConfig3AddPayloadEntry(struct __SysConfig3 *__single, struct __SysConfig3PayloadEntry *__single)
%s invalid argument. sysconfig and entry cannot be NULL
_Bool sysConfig3AddPayloadEntry(struct __SysConfig3 *__single, struct __SysConfig3PayloadEntry *__single)
%s Payloads with the deleted flag are only supported on version 0x%08X or higher.
_Bool _sysConfig3AddMetadataEntry(struct __SysConfig3 *__single, struct __SysConfig3MetadataEntry *__single)
%s sysconfig (0x%p) or manifest (0x%p) argument was null
_Bool _sysConfig3SetManifest(struct __SysConfig3 *__single, struct __SysConfig3ManifestEntry *__single)
%s sysconfig version (0X%08X) did not match manifest version (0X%08X
%s source string was NULL
char *__single __terminated_by(0)_allocAndCopyString(const char *__single __terminated_by(0), size_t)
%s data was zero length
uint8_t *__indexable_allocAndCopyData(const uint8_t *__single __counted_by(dataLength), uint32_t)
%s sourceData or destData was NULL
_Bool _copySysConfigData(const SysConfigData *__single, SysConfigData *__single)
%s sourceData was 0 length
%s sysconfig is NULL.
_Bool sysConfig3IsValidHMAC(struct __SysConfig3 *__single)
Invalid HMAC key. sysconfig->hmacKey.data = %p, sysconfig->hmacKey.length = %d. Did you register HMAC key?
%s Payload HMAC does not match.
%s Metadata HMAC does not match.
%s Full HMAC does not match.
%s Invalid HAMC key. sysconfig->hmacKey.data = %p, sysconfig->hmacKey.length = %u
_Bool sysConfig3CreatePayloadHMAC(struct __SysConfig3 *__single, SysConfigData *__single)
%s hmac is NULL
%s malloc failed.
_Bool sysConfig3CreateMetadataHMAC(struct __SysConfig3 *__single, SysConfigData *__single)
_Bool sysConfig3CreateFullHMAC(struct __SysConfig3 *__single, SysConfigData *__single)
%s Invalid HMAC key. sysconfig->hmacKey.data = %p, sysconfig->hmacKey.length = %u
alloc ws
free ws
ADDA
gfCf
acsf
gfCd
acsf
gfC0suafsuaf
laes
suaf
vppa
suaf
gfCeacseacse
foic
fumt
fsna
3@Uf
2@3D
r"$3
2"#C
r"$3
 `Zm
 `Zm
 `Zm
 `Zm
``HH
PP66
=XK]
??K]

 2`l
 2`l
TT
@T@T
@T@T
@T@T
@T@T
@T@T
TT
TT
uoui
uoue
tgpf
soui
0osr
1osr
2osr
3osr
lCPDprimary-calibration-matrix
lCLGgamma-calibration-lut
lCBDdisplay-backlight-compensation
lCLBbacklight-calibration
NULL
avsp
avip
ooipsoip
NULL
GCLC
/221xkcd
y6.:
1wfi
2wfi
1rdf
2rdf
main
interrupt
atsC
YAPS
ATEM
INAM
