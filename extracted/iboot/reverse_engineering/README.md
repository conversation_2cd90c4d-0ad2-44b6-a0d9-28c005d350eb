# iBoot Reverse Engineering Project

## Overview
This project contains the reverse-engineered source code for iBoot Stage 2 from the J313 device (Apple Studio Display), reconstructed from the binary `j313_iboot.bin`.

**Original Binary Information:**
- Version: iBoot-13822.0.166.0.1
- Target: J313 (Apple Studio Display)
- Size: 1,378,944 bytes
- Architecture: ARM64
- Copyright: 2007-2025, Apple Inc.

## Reverse Engineering Process

### 1. Binary Analysis
- **Hex Analysis**: Examined ARM64 instruction patterns at entry point
- **String Extraction**: Extracted 3,341 readable strings from binary
- **Function Identification**: Identified function boundaries and calling patterns
- **Command Discovery**: Found iBoot commands: `boot`, `bootx`, `reset`, `reboot`, `setenv`, `setenvnp`

### 2. Structure Reconstruction
- **Entry Point**: ARM64 assembly starting with interrupt disable and system register setup
- **Main Function**: Command loop with auto-boot capability
- **Command System**: Table-driven command dispatcher
- **Boot Process**: Multi-stage image loading (kernel, device tree, ramdisk, SEP)

### 3. Key Findings
- **Boot Paths**: `/boot/kernelcache`, `/boot/devicetree`, `/boot/ramdisk`, `/boot/sep-firmware`
- **Firmware Paths**: `/usr/standalone/firmware/` hierarchy
- **Environment Variables**: Persistent and non-persistent variable system
- **Security**: Image signature verification for all loaded components

## Source Code Structure

```
iboot_main.c        - Main entry point and boot flow
iboot_commands.c    - Command handlers (setenv, boot, reset, etc.)
iboot_boot.c        - Image loading and boot process
iboot_platform.c   - Hardware initialization (to be implemented)
iboot.h            - Header with all function prototypes and constants
Makefile           - Build system for ARM64 cross-compilation
```

## Key Functions Reconstructed

### Entry Point
```c
void _start(void) {
    // Disable interrupts (msr s3_0_c1_c0_7, xzr)
    __asm__ volatile("msr s3_0_c1_c0_7, xzr");
    __asm__ volatile("isb");
    main();
}
```

### Main Boot Flow
```c
int main(void) {
    printf("iBootStage2 for j313, Copyright 2007-2025, Apple Inc.\n");
    platform_early_init();
    platform_init();

    if (auto_boot) {
        cmd_boot(0, NULL);
    }
    command_loop();
}
```

### Command System
- **setenv/setenvnp**: Environment variable management
- **boot/bootx**: System boot with image loading
- **reset/reboot**: System reset functionality
- **printenv**: Environment variable display

### Image Loading
- **Kernelcache**: macOS kernel loading with signature verification
- **Device Tree**: Hardware configuration loading
- **Ramdisk**: Initial filesystem loading
- **SEP Firmware**: Secure Enclave Processor firmware

## Build Instructions

### Prerequisites
```bash
# Install ARM64 cross-compilation toolchain
brew install aarch64-elf-gcc  # macOS
# or
apt-get install gcc-aarch64-linux-gnu  # Linux
```

### Building
```bash
cd extracted/iboot/reverse_engineering/
make all          # Build the reconstructed iBoot
make disasm       # Generate disassembly
make compare      # Compare with original binary
make clean        # Clean build artifacts
```