# iBoot Reverse Engineered Build System
# Target: J313 (Apple Studio Display)

# Toolchain
CC = aarch64-none-elf-gcc
LD = aarch64-none-elf-ld
OBJCOPY = aarch64-none-elf-objcopy
OBJDUMP = aarch64-none-elf-objdump

# Compiler flags
CFLAGS = -march=armv8-a -mcpu=cortex-a57 -mgeneral-regs-only
CFLAGS += -ffreestanding -nostdlib -nostartfiles
CFLAGS += -Wall -Wextra -O2
CFLAGS += -fno-stack-protector -fno-builtin
CFLAGS += -I.

# Linker flags
LDFLAGS = -nostdlib -nostartfiles
LDFLAGS += -T iboot.ld

# Source files
SOURCES = iboot_main.c iboot_commands.c iboot_boot.c iboot_platform.c
OBJECTS = $(SOURCES:.c=.o)

# Target
TARGET = iboot_reconstructed
BINARY = $(TARGET).bin
ELF = $(TARGET).elf

.PHONY: all clean disasm

all: $(BINARY)

$(BINARY): $(ELF)
	$(OBJCOPY) -O binary $< $@
	@echo "Built iBoot binary: $@"
	@ls -la $@

$(ELF): $(OBJECTS) iboot.ld
	$(LD) $(LDFLAGS) -o $@ $(OBJECTS)

%.o: %.c iboot.h
	$(CC) $(CFLAGS) -c $< -o $@

# Disassembly for comparison
disasm: $(ELF)
	$(OBJDUMP) -d $< > $(TARGET).disasm
	@echo "Disassembly created: $(TARGET).disasm"

# Compare with original
compare: $(BINARY)
	@echo "Comparing with original iBoot..."
	@if [ -f ../j313_iboot.bin ]; then \
		echo "Original size: $$(wc -c < ../j313_iboot.bin) bytes"; \
		echo "Reconstructed size: $$(wc -c < $(BINARY)) bytes"; \
		hexdump -C ../j313_iboot.bin | head -20 > original.hex; \
		hexdump -C $(BINARY) | head -20 > reconstructed.hex; \
		echo "First 20 lines comparison:"; \
		diff -u original.hex reconstructed.hex || true; \
	else \
		echo "Original iBoot not found for comparison"; \
	fi

clean:
	rm -f $(OBJECTS) $(ELF) $(BINARY) $(TARGET).disasm
	rm -f original.hex reconstructed.hex

# Help
help:
	@echo "iBoot Reverse Engineering Build System"
	@echo ""
	@echo "Targets:"
	@echo "  all      - Build the reconstructed iBoot binary"
	@echo "  disasm   - Generate disassembly for analysis"
	@echo "  compare  - Compare with original binary"
	@echo "  clean    - Clean build artifacts"
	@echo "  help     - Show this help"
	@echo ""
	@echo "Requirements:"
	@echo "  - ARM64 cross-compilation toolchain"
	@echo "  - aarch64-none-elf-gcc and binutils"