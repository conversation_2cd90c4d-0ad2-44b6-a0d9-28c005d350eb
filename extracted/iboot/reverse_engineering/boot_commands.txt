bootx
boot-device
/boot
boot-ramdisk
boot-path
boot-breadcrumbs
auto-boot
boot-args
auto-boot-once
boot-command
one-time-boot-command
com.apple.System.boot-nonce
mute-next-boot
darkboot
force-upgrade-fail
failboot-breadcrumbs
upgrade-fallback-boot-command
boot-volume
alt-boot-volume
iboot-failure-reason
iboot-failure-reason-str
iboot-failure-volume
boot-image
recovery-boot-mode
iboot1-precommitted
Entering %s recovery mode, starting command prompt
command
%s boot, Board 0x%x (%s%s)/Rev 0x%x
bootdelay
aborting autoboot due to %s
iboot
reboot
Warning: Disabled hibernation for this boot due to side loading firmware
T = 0x0: load image from memory: addr/size
T = 0x1: search/load image in all block dev with 'type'
T = 0x2: load image from 'file path', match all type without type specified
fsboot
loadaddr
boot-stage
failed to
failed to execute upgrade command from new iBEC
failed to load
/boot/iboot_firmware
/boot/sep-firmware
/boot/devicetree
/boot/ramdisk
/boot/iBootData
/boot/kernelcache
failed to boot
ota-failure-reason
ANS2 command_accelerator tunables
command-accelerator-tunables
failed to read vendor-id/config-id, vid:%08x, config:%08x, rev:%08x
Coldboot
DCS Init     dcs_init_op_set_cal_cfg() freq_bin =%d, RL=0x%x, WL=0x%x
DCS Init     Calbration Config params init arg1=0x%x, arg2=0x%x, arg3=0x%x
dcs_init_process_condnl_block() called with condn_flag=0x%x dcs_init_loop_cnt=%d
dcs_init_op_if_density_id() called with density_id=0x%x, condn=0x%x, arg3=0x%x
dcs_init_op_if_density_id
Unsupported DCS_CONDN passed to dcs_init_op_if_density_id()!!
dcs_init_op_if_bytemode() called with bytemode=0x%x, condn=0x%x, arg3=0x%x
dcs_init_op_if_bytemode
Unsupported DCS_CONDN passed to dcs_init_op_if_bytemode()!!
dcs_init_op_if_rev_id() called with rev_id=0x%x, rev_id2=0x%x, condn=0x%x
dcs_init_op_if_rev_id
Unsupported DCS_CONDN passed to dcs_init_op_if_rev_id()!!
dcs_init_op_if_rev_id1() called with rev_id1=0x%x, condn=0x%x
dcs_init_op_if_rev_id1
Unsupported DCS_CONDN passed to dcs_init_op_if_rev_id1()!!
dcs_init_op_if_rev_id2() called with rev_id1=0x%x, condn=0x%x
dcs_init_op_if_rev_id2
Unsupported DCS_CONDN passed to dcs_init_op_if_rev_id2()!!
DCS Init     dcs_init_op_if_vendor_id() called with vendor_id=0x%x, condn=0x%x, arg3=0x%x
dcs_init_op_if_vendor_id
Unsupported DCS_CONDN passed to dcs_init_op_if_vendor_id()!!
DCS Init     dcs_init_op_if_other_id() called with other_id=0x%x, condn=0x%x, arg3=0x%x
dcs_init_op_if_other_id
Unsupported other_id passed to dcs_init_op_if_other_id()!!
Unsupported DCS_CONDN passed to dcs_init_op_if_other_id()!!
DCS Init     dcs_init_op_if_other_id() result:%x
dcs_init_op_if_soc_rev
dcs_init
target_pass_boot_manifest() used without target_init_boot_manifest()
boot-ios-diagnostics
UpdateDeviceTree: failed to find the /chosen node
UpdateDeviceTree: failed to find the /defaults node
secure-boot
boot-nonce
iboot-stage-one-tag
iboot-stage-one-variant
iboot-stage-two-tag
boot-chime-on-last-boot
boot-manifest-hash
trusted-boot-policy-measurement
display-boot-rotation
bootp-response
boot-type
boot-voltage
boot-adapter-id
boot-reason
boot-battery-state
load-kernel-start
sepfw-loaded
sep-boot-slot
sepfw-booted
fsboot-safe
iboot-handoff
dcp-auto-boot
error-handler
atc_apcie_offload_engine_fabric
cacal_find_right_failing_point
Memory CA calibration: Unable to find right side failing point for channel %d
cacal_find_left_failing_point
Memory CS calibration: Unable to find failing point for all bits on the left side
Memory CA calibration: SDLL ran out of taps when trying to find left side failing point
AMCC NONPLANE error: %s
AMCC PLANE%d error: INTSTS 0x%016llx AFERRLOG0/1/2/3 0x%08x/0x%08x/0x%08x/0x%08x ADDR %#llx CMD/SIZE/TYPE %#x/%#x/%#x AID/TID %#x/%#x
AMC PLANE%d error: AMCC_IRERRDBG_INTSTS = 0x%llx
DCS CHANNEL [%d, %d] error: INTSTS 0x%08x
disable-boot-wdt
cmd-results
usb cmd task
allowed-boot-args
boot-image-last
boot_image
boot_image_valid
boot-image is invalid
failed to enable decryption
failed to validate header signature (xnu panic on entry)
failed to generate SPTM key
failed to validate protected_metadata_hmac
failed to read preview image
hibernate_load_image_preflight
failed to compute PPL read-only region HMAC
failed to validate PPL read-only region HMAC
failed to compute image header hash HMAC
failed to validate image header hash HMAC
failed to validate segments
failed to restore segments
failed to find iBoot handoff region
failed to find PPL hibtext entrypoint
failed to populate xnu handoff region
hibernate_load_image
%s failed
failed to disable decryption on device
iboot-data
iboot_ifwX
iboot_nefw
object size check failed (%zu < %zu)
rsize check failed (%zu < %zu)
size check failed (%zu < %zu)
boot-object-manifests
secure-boot-hashes
/iscpreboot
/bootobjects
apfs-preboot-uuid
boot-uuid
boot-objects-path
external-boot
%s/%s/boot/%s
%s/%s/boot/active
%s: handle create failed
%s: load failed
failed to read/validate the socd push buffer section descriptors
failed to find the socd config parent node
failed to get socd push buffer base address
failed to get socd push buffer size
failed to init socd kernel push section, result %u
iboot-syscfg
allow-load
load-status
syscfg-seal-keys-loaded
pre-loaded
Translation table walk synchronous parity error (1st level)
Translation table walk synchronous parity error (2nd level)
Memory access synchronous parity error
Memory access asynchronous parity error
Synchronous parity error on memory access on translation table walk level
Synchronous parity error on memory access
Asynchronous parity error on memory access
Mailbox error
Unable to decode payload with error %d
Tag was not the expected Payload tag %llX
Decode sequence error: %d
Unable to decode metadata with error %d
Unable to decode manifest with error %d
Decode manifest sequence error: %d
struct __SysConfig3PayloadEntry *__single_allocSysConfig3PayloadEntryInternal(uint32_t, const char *__single __terminated_by(0), const uint8_t *__single __counted_by(dataLength), uint32_t, const char *__single __terminated_by(0), _Bool)
_Bool sysConfig3PayloadEntryGetData(struct __SysConfig3PayloadEntry *__single, SysConfigData *__single)
%s one of the hmacs were NULL. payload ptr: %p metdata ptr: %p full ptr: %p
struct __SysConfig3PayloadEntry *__singlesysConfig3GetPayloadEntryForKey(struct __SysConfig3 *__single, uint32_t)
_Bool _sysConfig3DeletePayloadEntryForKey(struct __SysConfig3 *__single, uint32_t)
_Bool _sysConfig3AddPayloadEntry(struct __SysConfig3 *__single, struct __SysConfig3PayloadEntry *__single)
_Bool sysConfig3AddPayloadEntry(struct __SysConfig3 *__single, struct __SysConfig3PayloadEntry *__single)
%s Payloads with the deleted flag are only supported on version 0x%08X or higher.
%s Payload HMAC does not match.
_Bool sysConfig3CreatePayloadHMAC(struct __SysConfig3 *__single, SysConfigData *__single)
%s malloc failed.
