/*
 * iBoot Command Handlers
 * Reverse Engineered from j313_iboot.bin
 */

#include "iboot.h"

// Environment variable storage
static env_var_t env_vars[MAX_ENV_VARS];
static int env_count = 0;

// Reset command handler
int cmd_reset(int argc, char **argv) {
    printf("Resetting system...\n");

    // Perform system reset
    platform_reset();

    // Should not return
    return 0;
}

// Reboot command handler
int cmd_reboot(int argc, char **argv) {
    printf("Rebooting system...\n");

    // Same as reset for iBoot
    platform_reset();

    return 0;
}

// Set environment variable command
int cmd_setenv(int argc, char **argv) {
    if (argc != 3) {
        printf("Usage: setenv <name> <value>\n");
        return -1;
    }

    const char *name = argv[1];
    const char *value = argv[2];

    // Find existing variable or create new one
    for (int i = 0; i < env_count; i++) {
        if (strcmp(env_vars[i].name, name) == 0) {
            strncpy(env_vars[i].value, value, sizeof(env_vars[i].value) - 1);
            env_vars[i].value[sizeof(env_vars[i].value) - 1] = '\0';
            env_vars[i].persistent = true;
            save_environment();
            return 0;
        }
    }

    // Add new variable
    if (env_count < MAX_ENV_VARS) {
        strncpy(env_vars[env_count].name, name, sizeof(env_vars[env_count].name) - 1);
        strncpy(env_vars[env_count].value, value, sizeof(env_vars[env_count].value) - 1);
        env_vars[env_count].name[sizeof(env_vars[env_count].name) - 1] = '\0';
        env_vars[env_count].value[sizeof(env_vars[env_count].value) - 1] = '\0';
        env_vars[env_count].persistent = true;
        env_count++;
        save_environment();
        return 0;
    }

    printf("Environment variable table full\n");
    return -1;
}

// Set non-persistent environment variable
int cmd_setenvnp(int argc, char **argv) {
    if (argc != 3) {
        printf("Usage: setenvnp <name> <value>\n");
        return -1;
    }

    const char *name = argv[1];
    const char *value = argv[2];

    // Find existing variable or create new one
    for (int i = 0; i < env_count; i++) {
        if (strcmp(env_vars[i].name, name) == 0) {
            strncpy(env_vars[i].value, value, sizeof(env_vars[i].value) - 1);
            env_vars[i].value[sizeof(env_vars[i].value) - 1] = '\0';
            env_vars[i].persistent = false;
            return 0;
        }
    }

    // Add new variable
    if (env_count < MAX_ENV_VARS) {
        strncpy(env_vars[env_count].name, name, sizeof(env_vars[env_count].name) - 1);
        strncpy(env_vars[env_count].value, value, sizeof(env_vars[env_count].value) - 1);
        env_vars[env_count].name[sizeof(env_vars[env_count].name) - 1] = '\0';
        env_vars[env_count].value[sizeof(env_vars[env_count].value) - 1] = '\0';
        env_vars[env_count].persistent = false;
        env_count++;
        return 0;
    }

    printf("Environment variable table full\n");
    return -1;
}