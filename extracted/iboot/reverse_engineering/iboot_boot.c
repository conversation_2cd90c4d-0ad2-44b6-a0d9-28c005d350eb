/*
 * iBoot Boot and Image Loading Functions
 * Reverse Engineered from j313_iboot.bin
 */

#include "iboot.h"

// Global variables for loaded images
static void *kernelcache_addr = NULL;
static size_t kernelcache_size = 0;
static void *devicetree_addr = NULL;
static size_t devicetree_size = 0;
static void *ramdisk_addr = NULL;
static size_t ramdisk_size = 0;

// Load kernelcache image
int load_kernelcache(void) {
    printf("Loading kernelcache...\n");

    // Try multiple paths based on strings found
    const char *paths[] = {
        BOOT_PATH_KERNELCACHE,
        "/usr/standalone/firmware/kernelcache.img4",
        NULL
    };

    for (int i = 0; paths[i] != NULL; i++) {
        printf("Trying path: %s\n", paths[i]);

        // Attempt to load image
        void *image = load_image_from_path(paths[i], &kernelcache_size);
        if (image != NULL) {
            // Verify image signature
            if (verify_image_signature(image, kernelcache_size) != 0) {
                printf("Kernelcache signature verification failed\n");
                free_iboot(image);
                continue;
            }

            kernelcache_addr = image;
            printf("Kernelcache loaded successfully\n");
            return 0;
        }
    }

    printf("Failed to load kernelcache\n");
    return -1;
}

// Load device tree
int load_devicetree(void) {
    printf("Loading device tree...\n");

    const char *paths[] = {
        BOOT_PATH_DEVICETREE,
        FW_PATH_DEVICETREE,
        NULL
    };

    for (int i = 0; paths[i] != NULL; i++) {
        printf("Trying path: %s\n", paths[i]);

        void *image = load_image_from_path(paths[i], &devicetree_size);
        if (image != NULL) {
            if (verify_image_signature(image, devicetree_size) != 0) {
                printf("Device tree signature verification failed\n");
                free_iboot(image);
                continue;
            }

            devicetree_addr = image;
            printf("Device tree loaded successfully\n");
            return 0;
        }
    }

    printf("Failed to load device tree\n");
    return -1;
}

// Load ramdisk
int load_ramdisk(void) {
    printf("Loading ramdisk...\n");

    // Check if ramdisk is required
    const char *ramdisk_path = getenv_iboot("boot-ramdisk");
    if (ramdisk_path == NULL) {
        ramdisk_path = BOOT_PATH_RAMDISK;
    }

    const char *paths[] = {
        ramdisk_path,
        "/usr/standalone/firmware/arm64eBaseSystem.dmg",
        NULL
    };

    for (int i = 0; paths[i] != NULL; i++) {
        printf("Trying path: %s\n", paths[i]);

        void *image = load_image_from_path(paths[i], &ramdisk_size);
        if (image != NULL) {
            if (verify_image_signature(image, ramdisk_size) != 0) {
                printf("Ramdisk signature verification failed\n");
                free_iboot(image);
                continue;
            }

            ramdisk_addr = image;
            printf("Ramdisk loaded successfully\n");
            return 0;
        }
    }

    printf("Warning: Ramdisk not found, continuing without ramdisk\n");
    return 0; // Non-fatal for some boot modes
}

// Load SEP firmware
int load_sep_firmware(void) {
    printf("Loading SEP firmware...\n");

    const char *paths[] = {
        BOOT_PATH_SEP_FIRMWARE,
        FW_PATH_SEP_FIRMWARE,
        NULL
    };

    for (int i = 0; paths[i] != NULL; i++) {
        printf("Trying path: %s\n", paths[i]);

        size_t sep_size;
        void *image = load_image_from_path(paths[i], &sep_size);
        if (image != NULL) {
            if (verify_image_signature(image, sep_size) != 0) {
                printf("SEP firmware signature verification failed\n");
                free_iboot(image);
                continue;
            }

            // Load SEP firmware to secure enclave
            if (load_sep_image(image, sep_size) == 0) {
                printf("SEP firmware loaded successfully\n");
                free_iboot(image);
                return 0;
            }

            free_iboot(image);
        }
    }

    printf("Failed to load SEP firmware\n");
    return -1;
}