/*
 * iBoot Header File
 * Reverse Engineered from j313_iboot.bin
 */

#ifndef IBOOT_H
#define IBOOT_H

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include <stdio.h>

// Constants
#define MAX_ENV_VARS 128
#define MAX_NAME_LEN 64
#define MAX_VALUE_LEN 256

// Environment variable structure
typedef struct {
    char name[MAX_NAME_LEN];
    char value[MAX_VALUE_LEN];
    bool persistent;
} env_var_t;

// Image types (from strings analysis)
#define IMAGE_TYPE_KERNELCACHE  0x6B726E6C  // 'krnl'
#define IMAGE_TYPE_DEVICETREE   0x64747265  // 'dtre'
#define IMAGE_TYPE_RAMDISK      0x72646D73  // 'rdms'
#define IMAGE_TYPE_SEP_FIRMWARE 0x73657066  // 'sepf'
#define IMAGE_TYPE_IBOOT_DATA   0x69626474  // 'ibdt'

// Boot paths (from strings)
#define BOOT_PATH_KERNELCACHE   "/boot/kernelcache"
#define BOOT_PATH_DEVICETREE    "/boot/devicetree"
#define BOOT_PATH_RAMDISK       "/boot/ramdisk"
#define BOOT_PATH_SEP_FIRMWARE  "/boot/sep-firmware"
#define BOOT_PATH_IBOOT_DATA    "/boot/iBootData"

// Firmware paths
#define FW_PATH_BASE            "/usr/standalone/firmware"
#define FW_PATH_FUD             "/usr/standalone/firmware/FUD"
#define FW_PATH_IBOOT_DATA      "/usr/standalone/firmware/FUD/iBootData.img4"
#define FW_PATH_DEVICETREE      "/usr/standalone/firmware/devicetree.img4"
#define FW_PATH_SEP_FIRMWARE    "/sep-firmware.img4"

// Function prototypes

// Main functions
void _start(void);
int main(void);
void platform_early_init(void);
void platform_init(void);
void command_loop(void);
int parse_command(char *cmdline);

// Command handlers
int cmd_boot(int argc, char **argv);
int cmd_bootx(int argc, char **argv);
int cmd_reset(int argc, char **argv);
int cmd_reboot(int argc, char **argv);
int cmd_setenv(int argc, char **argv);
int cmd_setenvnp(int argc, char **argv);
int cmd_printenv(int argc, char **argv);
int cmd_help(int argc, char **argv);

// Hardware initialization
void gpio_init(void);
void uart_init(void);
void i2c_init(void);
void spi_init(void);
void usb_init(void);
void storage_init(void);
void security_init(void);
void heap_init(void);

// Platform functions
void platform_reset(void);
int get_boot_mode(void);

// Environment functions
void load_environment(void);
void save_environment(void);
const char *getenv_iboot(const char *name);
int setenv_iboot(const char *name, const char *value, bool persistent);

// Image loading functions
int load_kernelcache(void);
int load_devicetree(void);
int load_ramdisk(void);
int load_sep_firmware(void);
int load_iboot_data(void);

// Boot functions
void prepare_boot_args(void);
void jump_to_kernel(void);

// Security functions
int verify_image_signature(void *image, size_t size);
bool secure_boot_enabled(void);

// Memory functions
void *malloc_iboot(size_t size);
void free_iboot(void *ptr);
void *memcpy_iboot(void *dest, const void *src, size_t n);
int strcmp_iboot(const char *s1, const char *s2);

// String functions
char *gets(char *s);
int printf(const char *format, ...);

#endif // IBOOT_H