iBootStage2 for j313, Copyright 2007-2025, Apple Inc.
RELEASE
iBoot-13822.0.166.0.1
 P@y(
T`>@
Ta.@
4v&@
Ta>@
4u6@
T(@
h.C9
Th*C9
h6C9
h.C9
h6C9
Th>@
B@9i
T({x
R(AH
Th{x
2C9(
R_A,
J88(
Tjzh
+|S
T jh
4vzt
7O9?
iW@9
!@)?
)@)?
)@)?
S+Ij
*(I*
S)Ij
+*k!
R(I3
h~S
7hz@
7*ih
**i(
Tj~|
3+i(
TLa@9
7Le@9
*z~}
TT[8
+)@6
R+)G)H
84@yX
58@9
 7h.@9
i*@9
i.@9
Tk*@9tq
Tj6@yJ
h6@yH
h6@yH
h6@yH
54@yU
55@9
iw8)
i78h
iw8)y
i78hO@
)@9j"
74@yW
57@9
=@9)
*=@9
74@yW
57@9k
a@9)
74@yW
57@90
+ij8k
2+i*8
+ij8ky
+i*8
)-@9
,)@9
)@9j!
74@yW
57@9
4h&@
Si&@
Tkzj
rlI*
Tkzj
SHI)
S(I*
rKI)
SuI)
SiI,
SHI)
R*I(
rLI+
Tjji8H
7h3@9
5)9@
T*ih8kjh8j
*jj(8
TBkyx
+I9h
+I9)
;I9I
%J))
A@9h
%J))
hJ@9
)!H9
(+D9
HoA9
IgA9?
T)+D9
TCg@
5HsA9
BwA9
}B9?
)!D9?
T('@
T*%@
6@9H
(!19
gF9h
gF9H
gF9H
cF9W
OJ9h
GJ9H
4hFA9I
4hJA9
4hNA9I
T4kx
5bjv8H
hj68
TLii
Tlii
)Gzc
rd"Hz
Hz(f
Hzh.
HzhN
Hzh,
Hzh.
HzHf
Hzhm
HzHn
Hzhl
HzhL
g@9H
jx8/
rciM
T`2@y
h6@ya
T`>@ya2@y
4`>@ya2@y!
h6@y
Th:@y
(3I9h
4zkv
H;@y
7A3@y
4H3@y
3@y?u
'@) 
ThB 
@z 
@6s$
eN9'
YN9h
@9)]
)AN9
Si3I
T*E@
(A@9_
#@9?
@9)i
;ih8
*C@9J
l)@9
Tl!@9k%@9
R)C@9_
*)@9_
K!@9J%@9J
%@9h
T+ih
T*ih
T"{x
T"{x
&P9 
_8n!
*!Q9H
*a@9
6)!@
ISA9?
TnzO
*h1B
Z))=
!W))=
K!C)J%B)
)!@)(!
THSA9
4hzO
HSA9
HSA9
ISA9?
HSA9
ThzO
HSA9
*.N,
Tyz:
Th{y
(%@)
@6;<
h6Q;
q3/
TQ{p
6q{p
TV{0
KQi+
TLi+
TLik
)QA9?
JQA9
Tp{o
Ts{/
KPi+
TLik
)QA9?
)@za
T*ih
TMih
TH{u
JQA9_
HQA9
TJMM
r)a 
Az9I
Thzz
Tjjh
5jih
Rjj(
@6`*
r)q!
T*}C
Qji)
G6l%
T*}C
Qji)
T)QA9
A*-
T"{v
T)kh
7h{x
Ti{h
6h{w
)*)u
Ti{h
Tjkh
7i{h
7h{x
6h{x
Tjkh
6hB_
xI{(
@9KM 
'f)?
T({O
QA9s
D)B<
FA)B<
@)1"
A)B<
FA)B<
@)1"
6({u
(@))
(A))
QA9I
YA9_D
T+{h
T:{(
vz(x
T+kh8J
)a*)
j%A)J=
*k)@))!
rJa!
)!*)
Th{@
#A):
Th{@
TH{@
T!{z
S`J(
S`J(
S`J(
T!{z
TH{@
Kw3
T!{z
S`J(
S`J(
S`J(
KX3
T!{z
SX!@
Aqd)@zqS
*,}
@9(
Rb*@
T*ih
 ih8
 ihx
5h"@
JyA
(A>9
1@yi2
1@yi
1@yir
TKQ_
T`r@y
`r@y 
B)c}
)BzHq
 Hza
9v^@
q")Fzi
q")Dzi
6A9)
jp9h2
Q(I*86k(8
*@y(A
5v^@
"B)!}
2@y(A
TLMt
hj@9
B9?1
h~E9
2@yi
)[@9)
*[@9J
*i@9_
)Q@y
Y@9)
)@y*
(!?9
K|S,@
T`*@
T`.@
R+ij
*(i*
+EAy
_By
@q51
R+ij
2KI)
Tij(
qM*m
aN9h
Ib9h
Ab9h
Mb9h
(%"9 
Mb9H
jl8pyp
TPyi
TPyi
hbe9
hfe9h
R`f%9
!@9H
A@qa
S)i~
`"qh
!@)?
)-T9
9`2T9
@yA
}@y_
5J!@
xh*@
!@9(#
@zA+
~@yI
~@yI
~@y)
~@y?=
9h&E
Ti"E
}@y_=
%B)?
%B) 
y`<@
Tlyh
hFW9
 MW9
hFW9
Kysc
Kypc
Kymc
Ky+c
Kyzb
Kyob
6H{h
$C);
hfLy
`fy$
@yK]
7t>@y
b>@y
i>@y
i>@y
Th"@9
h"@9h
"@9h
 @9(
S@9@
3@9@
3@9`
S@9`
5h&@
5h&@
2j.C)_
@q)%
A@q(
l6M)
l6R)
rk2C)
k&D)k
5h&@
H'@)
7hv@
7hr@
6g>@
6gB@
Qhr@
T`z@
4hz@
)Q)*
Qkr@
Thz@
"D9h
_)Ikc
T+}J
C@9h
iz8h
T(k|8
"@y)
h"Gy
!@q(
Th:Gyi>Gy+
TiA*
5h&@
K0@y(
H@@y
6KD@y
TKD@y
LP@y
LT@yi
kJ%
jH$@
JX@y)
J\@y?
KvF@yuB@y
#u)h
4h&@
*AzJ
Th&@
7($@
*@@y
+X@yk
T+T@yJ=
*X@y?
)@@y?
7*$@
,X@y-\@yJ
_!.k
)qAy?A
Kik8+
Kik8+%
6Y9 
?A9H%
A97
!9Y9
A9)%
A9(!
#A9
;A9)
;A9)
7A9k
T)$@
4)(@
9`:@
S@I+
kB@9
@9)A
@9)!
@9kA*l
@9k!*l
TaB(
vB@y
*a|r
J@yU
!BzH
s@yh
s@yH
T`zv
Th{u
j.8
4y{u
!@y1
Y@9,J 
5hB@y
6nB@yn
*nB@y
F@yXC
F@yh
h@69
6*m@
F@yh
B@y(
T {xx
hB@y
RHmt
)]@y
hB@y
@y)A
Y@yi
T(G@yh
Y@y*
TjF@y
B@yH
B@yi
Thyi
5(@
@@yI
TLyi
Rmil
2mi,
hId8)%
Ti!@
jQ@9oU@9qY@9a!@y
!Jzb
TLyh
@9m!
Thjy
7hjy
Thjw
@9k%
mihx
ThFC
J%@9
R(s]
h.@9(
'E)h2
a.@9b
4ajv
T`3_
UJ9?
QJ9?
HCB9
TK1@9k
7`W@
6uW@
yKqA
6hW@
7@_B
3kB 
hf@9
*A@9*
'Y9\
;@yw
&C);}
:@yJ
9hb@9
`6@y@
)AB9?
@)K=
T(@
G@9d
V9GWB
KJ}
HShF
Rh&@y
hF@9
ET9H
*1Jy
T)Q@9
MT9H
Q@9h
 @9)
S(qH
hr@9
hr@9
R*i(
`F@y
`F@y
`B@y
`B@y
`B@yg
`F@yZ
T) @
T)0@
T)@@
lih8M
iw8*
iv8*
jv8(
Tlzix
ijjx?
T*yh
i*A9*
9hF@
Ti:@
AW9?
c@9S
+@9_
1d(@
@9i"
@9)!
TB@@
Iii8
Hih8(!
I`8{S
@x)}
*?C 
TH)B9
ik8o
i+8*
dW8i
@8+q
B9*|
B9)!
?@@qb
h+B9H
7h/B9
Rh#B9a
h'B9
(B9h
;@9`
@yY#
@y)%
jh8?
A`9H
Jik8k
ji,8
i)8h
ii8h
 9`.
L@9J
L @9J
L$@9J
L(@9J
L,@9J
L0@9J
L4@9J
L8@9J
L<@9@
!TJs
4!TJs
KBx@L
Td_D
!nB80n
8pnD8
Te_D
 nB80nd
!.f80nd
8pnD8
QoBDnE@
Te_D
 nB80nd
!.f80nd
8pnD8
TBx@L
Td_D
!nB80n
8pnD8
QoBDnE@
e@9?
4h#@
`@9H
5hb@9
hf@9
7hf@9
)a@9?
e@9?
`@9(
`j685
jh8?
@9:-
aAk@
6h"@
6h6@
4h6@
1@9h
))A)
 Yhx
))A)
{2@y
h:@yi
1@y)
)A)+a
l&B)i
i"A)
1A)k}
4h*@
,A)I
&B)I|
)A)+
T-e@8l
TMQ@9l
TH)@
TMa@9l
6hR@
T*!@
T()@
T)1@
)A)+
2@9i2@9
5h&@
T(@
 B)k}
"B))}
T+ih
*ih8
Kih8,
T*ih
Tajt
Th"@
hBC9
@9ISC
(SC9(
(SC9
*Q)?
)MC9
MC9(
THkw
MC9s
TKii
_A@q
?A@qHB
A@qh:
Tiz(
TkZk
Tzz{
I#@)*}
Th3^
hs^8
h'~)j/})
jsx(
v2@y;
4h:@y
h6@y
5y:@y9
Th:@y
6`6@y
h>@y
@y!A
f9?!
i)8)
@9)}
@y?E
#@9t
@yhB
3Yzb
qB) 
(ih8
(ih8
(ih8
(ih8
(ih8
T)hh
Kik8+
Kik8+%
_h(8
(it8
#@9(
#@9(
#@9(
T)}+
kk8,
T!A"
Mz0}
TH'@x
j989
@9
Tkjj8
i*8J
i)84
T)hh8
h38 
Th$?
T`ju8p
4`ju8U
RC@#
R(C@
Aj9)
A*95
`2@9
4j:@9h
j989
j989
j989
TI'@
6A'@
*C'@
jh8?
*A))
&A) 
&A)"
qH/@
5I/@
4IC@
&A)(
THC@9
9A+@
j(8Z
TH3@
TA+@
7h.@
.F)J
*F)C
THC@9
T[sF)
I+F)C
&F)(
*F)B
&F)(
jj8k!
B)I-
yk}
R))@)-
.@y(}
_x!@
%@xp
`%@x`
T*A^
TJyk
)EA9?
T*A^
TJyk
IA9H
'@8>
@)(]
@)H!
SJA@QJ!
@9i[A9(
3i|P
(I*8
(Y*x
*1@)
Kykxj=
ThV@
ThV@
i*8J
5@9I
)1@9v"
6@9(
Thzh
y!Y(
@yz
Th{y
0@9?
4`"@
TH#@
T5#@
MF9?
Tw"@
Tt"@
Tb"@
UF9H
Zh{9
)#@)9
4C/@
-C)5D)
AF) 
7H@
7H'C
7H'F
4H'@
7h&@
YF9H
(@@9
@@9?
*A@9_
A@9?
HC@9
R)'*
T(kw
gH)*w
/@y?A
/@y{C
`ju8
?@9H
kw8K
s@9I
TjB4
W@y)
lij8
5b.@
5`F@
T5?@
5 KA
5 KA
T KA
5 KA
yh*@
4h.@
5`F@
5`F@
h:@y
53KA
cO9(
jh8_
cO9h
Ki(8
TQu~
1@9?
TuB(
uuid
trap.S
Invalid SP in exn handler
kb!Tz
k"!Tz
T?7@
-@9I
-@)H
1Iz"
,@)k
TKA+
,@9
qD)Cz 
T,{w
X7-{7
H/@9
4H/@9
I+@)
TC#@9D'@9H/@9
H/@9
TH/@9\
6I+@)C+@9
_xtB^
_@9*
j@CJ*
@9iB
hB@9
Tm1C
Tm1D
Tm1E
Tm1O
Tm1G
Tm1F
Tm1H
Tk1B
@9( 
ThN@
9wF@
Tpyp
Th6@y
qW9)
(3@y
(3@y
(w@9
T({@9
Th"@y
|@9(
B9ir@9?
h2@y
Th~@9H
h2@y
T?C@q
+ij8h
*(i*8
TKyh
TKyh
B9(#
Q|K4
_+Hj
"Ayh
$Ayh
2Ayh
T(CJ
4h*Ay
0@y)%
hJ@9H
hB@9
ThF@9
4h:@yH
B9i"
`N87
@9(q
*_5@
@9(-
p@9_
x@9h
7)'Ay
4)/AyI.
T)ywx
 /AyA
2Ay?
Ti*Ay
T`>@
`*Ay
Jk!9
@@9(9
)3@y
B9)!
!3kA"
@9H
qW9)
T(w@9
(s@9
)3@y
6<7@y
4(s@9
B9-s@9
B9)!
**7@y
S?+Ij`
h2@y
Th~@9
h2@y
8@yH
B9I#
h6Ayh?
fhj!
S()H
0@y?
0@yk
4@y?
0@y)
TLyk
ySy(
B9k!
*,7@y
D@yS
@yhB@yiF@y
mN@y
yLy-
!.kH
T)!Ay?
hF@y
T(@
@@y?
L@y?
@yiF@y
TmJ@y,ym
oB@y
ymF@y
!-kb
S?aEq
T(#Ay
T)3Ay?
T(C@
hB@yiF@y
yhF@y
"@9b
2@y?
0@y_
@q@
'B)K
)*(yH*
*B))}
EA9?
@A9+
@A9h
T_C 
Thju8
Ri*@
Ri*@
5h2C9HE
(-HQ
K@9i
S)5}
;@9h
2C9h
B@qi
S)j}
bB9h
h"B9
7h&@
7h&@
R <@
@y"<@
!@@yb
%H9h
5@y+
))H9?
h"K9H
%K9?
js8bu
*%K9(
"K9I
Thjv8
T`jv8
(i48
(19
i)8+
v@94
(59
i)8i
?@9h
T`{z
`@9h
)=F9
4`RA
=F9h
a@9i
a@9?
*a@9_
*a@9_
6a"@
T`"@
RjZA
7`n@
TKyh
la@9L
7`*@
hvA9h
Ri&@
T)MA
T*yh
Ka@9K
K"}}
$G)?
2F9H
jh8?
8hrA9
4hzA9
F9*=
4(c@
TKii8
T_h(
i2@yj6@y
4w"@
Th&@
7`.@
,*J%
s@9(
#Q8H
+**%
5H#@
4H#@
T?@@
Ti"@
T?A@
T)P"
ThzA
T)P"
T)P"
ThjA
_#(k(
T)P"
@8j!
"8ki
I!@yI
4hk|
7`NA
T`NA
*f"
 6hB@
5br@
T`n@
@9hR
hBF9
hBF9
5hv@
T`v@
5h~@
4h~@
Thz@
4h~@
Thz@
h"@y
*ij,
5b*@
T`&@
87`B
g6h2@
T`.@
hFF9
?@9i
_9h(6
@9nyp
@9nyp
@8nyp
TMhi
Mhi8
!NB( nB(`nB(
JHj8
Tk$@x
Jzh:
LklxM
Ak#(
5 .!
!( .!(`.!(
80.
B80.B
!".0
B80.B
!".'
B&.C
TlJi8
K%@xl
@9*!
i"K)
Tm2H
ThZB
Tl2@
+ikxk*
Th6@
5h&B
T*i(8
T+i(8
T,i(8
h2I)
Tn6B
Tl:B
Tm2@
Tn"@
Tn"@
)h"@
x^K)
Tk*H
Tj2@
C@yi
Th6@
5i"B
T*i(8
T+i(8
T,i(8
j.I)X
TjfB
Jykx
kynx
Tl"@
Tk*B
zjxK
Tl"@
)h"@
@8Ki,8
TI}D
@8/yn
jh8M
4Eyhx
@9)%
@9J%
@9k%
jh8@
ThB)
@81"
T(@(
zgI)
@ymklx
Rnkmx
`NJa
5jG@
Viy8
Viy8
Viy8
Thkt
I)hG
Rik*
Thkv
ThGC
Ri[_
Riki
+Kj{
?yk=
+Kj{
+Kj{
?yk]
+Kj{
a*h
ThCC
4i3@
@9){h
?AyL
CAymjlx
Rnjmx
`NJa
uztx5
Zit8
Zit8
Zit8
.jo8
RC@#
SC[j
=c$.b
TP@`
N4B`
Np2p.
5N*>
1:0.
SS[k
1:0.1
SU[l
s:0.w
80.
TX%@x
!8kb
Rs"8
D"qa
,QJ`
@"ql
6-F@
6-&@x
"@9(
6@9)
>@9(
.@9)
:@9(
&@9)
2@9(
Z(N!x(N
Z(NBx(N
Z(Ncx(N
Z(N!
Z(NB
Z(Nc
!n@H(N
=@H(N
T($@
T($@
@9K!
@9kA
@9JA
+i`8Li`8
Ki`8,i`8
RKh*8
_h*8
_h*8
RKh*8
_h*8
_h*8
T?i(
jj8k
Kih8k
JKi(8
9@)
Q$@)
@4A)
JLC)
O D)t
F,E)
T@F)
Mil8
T+x)
Tjz(
Tz"A
"nB@
jhi8
hi8+hh8)
jhh8
hh8hi8j
r4@)
T8O0
o2:an1:a.
:a.T
W@)s
n~)<
n~)<
,A)4B)
<C)0
T8O0
/2:a.
:a.1:anT
Jg*GJ
x@)Z
RIh(8Hhw
_h(8Hhw
_h+8+
RAxh
SDxk
3Dxm
3Qxo
ic8?
hh8?
RCx(
 @)*
X(N!x(N
X(NBx(N
X(Ncx(N
X(N!
X(NB
X(Nc
%n`(
H(N!h(N
H(NBh(N
H(Nch(N
H(N!
H(NB
H(Nc
%n`(
}Ut]
$o,
vRQ>
8STs
LwH'
= ((^v
NA((^rB
Nb((^rB
((^rB
 nx
= ((^v
NA((^rB
Nb((^rB
((^rB
= ((^v$
NA((^rB
Nb((^rB
((^rB
= ((^v4
NA((^rB
Nb((^rB
((^rB
uVxO
1VjOk
4nc@
TCOm
uVxO
1VjO)
4nc@
1TCO-
uVxOk
1VjO
5nm9
4nc@
QTCO
uVxO)
1VjO
5n-9
4nc@
qTCO
uVxO
1VjOk
4nc@
TCOm
uVxO
1VjO)
4nc@
TCO-
uVxOk
1VjO
5nm9
4nc@
uVxO)
1VjO
5n-9
4nc@
RRRRRRRRRRRRRRRR
oCh@LB
nAH(N!
!nA`
!nA@
oCh@L 
nAH(N!
!.A`
!nA@
oCh@L 
nAH(N!
!nA`
!nA@
NGH(N
'nG`
'nG@
x(N!x(N 
x(N 
H`@9
ij@9
9hb@9i
ij@9
hb@9i
h@9h
`@9)
d@9i
9hb@9i
Rhb@9
7@9ib@9)
9hb@9i
7hb@9i
9hb@9i
6I=@
T(|~
ij@9
hj@9h
h@9h
hj@9h
h@9h
(ihx
+@y)
Thf@9h
7hj@9
"@yi
S@9H
mii8m
T|"A
2?@@
aErO
aErO
fena
enar
fpoa
poar
feva
evar
fxfg
xfgr
fpsi
psir
fois
oisr
fpmp
pmpr
0pcd
2pcd
2pcd
2cdr
1pcd
2pcd
2pcd
2cdr
root
storage
rtsC
revC
revC
pdcC
xehC
ktrC
xbmC
tscC
AgrC
8grC
MgrC
mitC
aehC
vr8^T:l)U
U8*T
]o,&
h&(z[
=Mk`
p<5a
,f>'
xj)9
sEy8
=*\\E)
}Q_iz
^1kW3
E( V
I0$r
mNrS#doMngeRSHLCukskUKSWomla
gaid
cebi
tobi
tsti
trep
tmbr
tlhp
foic
fumt
oicr
umtr
snar
fsna
AeeQ
firmware
syscfg
anvram
#tvxz|~
#tvxz|~
 @(=
(@(=
0@(=
8@(=
@@(=
H@(=
P@(=
X@(=
`@(=
h@(=
p@(=
x@(=
 @p;
(@p;
0@p;
8@p;
@@p;
H@p;
P@p;
X@p;
`@p;
 ???
 ???
 ???
 ???
 ???
 ???
 ???
 ???
%
%
%
%
0Mk4
VX!8
VX!8
0Mk4
VX!8
VX!8
0Mk4
VX!8
VX!8
((d
((d
((d
!"33DDD
3CDUUfff
dddddddd
13DDUUU
????
4#1"#5
Su&E4cC
????
 @ `
m
@DDD
tsrtlaesgfCfgfCdvppagfCektpafpcdpcdefoicfumtMEPLHTLdCIBd1wfi2wfiwfengfCtgfC0tbplLCDAFSPdSALrCALiTSRP1rdf2rdfgfCbLACUSCIBlCDxHTLcACmHrcSdgfCLCIBTlCmHgfCjgfCcgfCm
iBoot-13822.0.166.0.1



????p
00hCW1hCW
(((((
AAAAAA
BBBBBB
%(null)
<<WIDE CHAR>>
<<WIDE STRING>>
<<N SPECIFIER>>
<<FLOATING POINT>>
<<PTR>>
3D3287DE-280D-4619-AAAB-D97469CA9C71
nvram
2nvram
common
2common
system
2system
0ghc1ghcFtab0tab1tabPylgdqilogol0wpl1wplmcer1glr2glroglr
aidi
rvcR
rkosftab
sik-
tsrtcstrctsb
0K1'0%
Apple Secure Boot Root CA - G61
Apple Inc.1
190821182918Z
440821182918Z0K1'0%
Apple Secure Boot Root CA - G61
Apple Inc.1
N2|sn
n/wyo2
xVU1
aXYY
B0@0
gN5 
Fydy
QgQ$"
i2[y
Fe,.o
:{Hc
g^\o
0K1'0%
Apple Secure Boot Root CA - G21
Apple Inc.1
141219201310Z
341214201310Z0K1'0%
Apple Secure Boot Root CA - G21
Apple Inc.1
I-ix_'
_l`.q_
:aR
w[\J
BW=B
e\y4
+?Vh
B0@0
P(e
P2ff?
0S1'0%
Basic Attestation User Root CA1
Apple Inc.1
California0
170419214156Z
320322000000Z0S1'0%
Basic Attestation User Root CA1
Apple Inc.1
California0v0
SOI2
v3]D
B0@0
0O1+0)
"Apple X86 Secure Boot Root CA - G11
Apple Inc.1
170322214243Z
170323214243Z0O1+0)
"Apple X86 Secure Boot Root CA - G11
Apple Inc.1
\q(8
w&]Be
MXt?
`/-E7
@=%B
!."{
C \x=>
5bbc
B0@0
Fh[;
s#NU
p%OR&
comb
trst
rvok
fdrd
secb
trpk
IM4C
CRTP
mcmb
YAPS
ATEM
INAM
PTRC
KBUP

HpJX
D7q/;M
}Uo
+Yo,
&\8!
* qW
LwH'
L*~e
}Ut]
$o,
vRQ>
8STs
LwH'
|6*)
g&3g
[
"
D7q/;M
}Uo
+Yo,
&\8!
* qW
LwH'
L*~e
%llx:%d
diags-path
bootx
boot-device
%s%c
/boot
dt-path
sepfw-path
seppatches-path
%s/%s
boot-ramdisk
diagnostics
kcgen
recoveryos
roothash-path
%s%s
system-volume-auth-blob
base-system-roothash-path
base-system-volume-auth-blob
boot-path
fuos-path
%s/%s.%s
panicmedic-auxkc-present
true
false
panicmedic
auxkc-path
boot-breadcrumbs
nvme_firmware0
nor0
ibd-path
chosen
non-apple-or-untrusted-code
security-downgradable
protected-data-access
enable-sep-rm
enable-user-rm
Permission Denied
upgrade
darwinos-ramdisk
macos-darwinos
bs-tc-path
tc-path
iBootData
Trustcache
ACIO
RTBuddy
WCHF
RestoreANS
auto-boot
boot-args
debug-uarts
apt-carveout-size-mb
auto-boot-once
backlight-level
backlight-nits
boot-command
one-time-boot-command
com.apple.System.boot-nonce
ramrod-kickstart-aces
stress-rack
StartupMute
StartupMuteAccessibility
SystemAudioVolumeExtension
SystemAudioVolumeSaved
mute-next-boot
dev-unset-debug-enabled
idle-off
is-tethered
darkboot
force-upgrade-fail
ota-breadcrumbs
failboot-breadcrumbs
recovery-breadcrumbs
com.apple.System.tz0-size
com.apple.System.rtc-offset
upgrade-retry
preserve-debuggability
upgrade-fallback-boot-command
boot-volume
alt-boot-volume
policy-nonce-digests
boop-storage-nonces
iboot-failure-reason
iboot-failure-reason-str
iboot-failure-volume
boot-image
recovery-boot-mode
recovery-reason
system-passcode-lock-blob
prev-lang:kbd
panicmedic-timestamps
panicmedic-telemetry
panicmedic-engaged
iboot1-precommitted
Entering %s recovery mode, starting command prompt
iBootStage2
command
idleoff
Remote
Local
Microkernel 
=======================================
:: %s%s%s
%s boot, Board 0x%x (%s%s)/Rev 0x%x
BUILD_TAG: %s
UUID: %s
BUILD_STYLE: %s
USB_SERIAL_NUMBER: %s
=======================================
bootdelay
aborting autoboot due to %s
tethered restore
user intervention in previous stage
user intervention
iboot
recover
recover-system
reboot
reset
bgcolor
setpicture
disable
usage:
filesize
filesize variable invalid or not set, aborting
Warning: Disabled hibernation for this boot due to side loading firmware
firmware
devicetree
getenv
saveenv
setenv
setenvnp
clearenvp
ramdisk
rsepfirmware
picture too large, size:%zu
Memory image corrupt
flipbook(1): image_offset=%d image_count=%d
flipbook(2): image_offset=%d image_count=%d
setpicture optmask [<addr> | *] [<size> | *] [<image_offset> <image_count>]
setpicture optmask [image_4cc] [<image_offset> <image_count>]
setpicture optmask <file path> [image_4cc]
optmask: 0xVVVV00TM: VVVV=voffset override, T=operation type, M=operation mask
VVVV = vertical offset in pixels (0=no offset=iBoot default behavior)
T = 0x0: load image from memory: addr/size
T = 0x1: search/load image in all block dev with 'type'
T = 0x2: load image from 'file path', match all type without type specified
M = 0x1: update the image on screen
M = 0x2: don't clear previous images
M = 0x4: restore mode image
M = 0x8: select flipbook image with image_offset/image_count, T!=2
fsboot
build-style
build-version
config_board
board-rev
loadaddr
ramdisk-size
/usr/standalone/firmware/FUD/iBootData.img4
boot-stage
/System/Library/Caches/com.apple.kernelcaches/kernelcache
/System/Library/Caches/com.apple.kernelcaches/kernelcache.auxkc
/System/Library/Caches/com.apple.kernelcaches/kernelcache.custom
base-system-path
/usr/standalone/firmware/arm64eBaseSystem.dmg
/usr/standalone/firmware/devicetree.img4
/usr/standalone/firmware/root_hash.img4
/usr/standalone/firmware/base_system_root_hash.img4
/sep-firmware.img4
/sep-patches.img4
/usr/standalone/firmware/FUD/StaticTrustCache.img4
/usr/standalone/firmware/FUD/BaseSystemTrustCache.img4
failed to
mount upgrade partition
failed to execute upgrade command from new iBEC
unknown
failed to load
/boot/iboot_firmware
unused firmware
/boot/sep-firmware
sepfw
/boot/devicetree
/boot/ramdisk
/boot/iBootData
/boot/kernelcache
kernelcache
finalize memmap
failed to boot
%s %s
ota-result
ota-failure-reason
acio-cpu%d/iop-acio%d-nub
__TEXT
__DATA
ANS2 afc_aiu_ans_dual tunables
afc-aiu-ans-dual-tunables
ANS2 command_accelerator tunables
command-accelerator-tunables
ANS2 storage_lane_common tunables
storage-lane-common-tunables
ANS2 ccu_security_interrupt tunables
ccu-security-interrupt-tunables
arm-io
region-base
region-size
NFMT=
quiesced
ANS2
ans/iop-ans-nub
DCS Init     REG(%p) <- 0x%08X  (Single-Reg-Write)
%llx:%d: spin loop timeout
DCS Init     REG(%p) <- 0x%08X (x%d)
DCS Init     REG(%p) Poll: Mask (0x%08X) == 0x%08x ...
 [data==0x%02x] 
DCS Init     MRCMD(0x%08X)[%dx chan][%dx rank]
 SEND: 
->%d%d 
 POLL: 
%d%d! 
density_to_Gb
unknown LPDDR4 density %d
dcs_get_memory_size
DCS Memory Size grab has jumped the gun
dcs_get_memory_info
failed to read vendor-id/config-id, vid:%08x, config:%08x, rev:%08x
sdram vendor id:0x%02x rev id:0x%02x rev id2:0x%02x
sdram config: width %d/%d Gbit/type %d
sdram config: bytemode %d 
unsupported DRAM density: %d Gbit  (with Chan==%d x Rank==%d yields %d MB)
dev_density: 0x%x
Memory_size: %llu bytes
DCS Init Lib Built for: iBoot SOC
DCS Init [%s Build] for ChipID==0x%04x chipRev %c%c (#DCS==%d, #Rank==%d) [%s] using %s
iBoot
Coldboot
UpgradeInit
??BootType??
DCS Init     Calibrating Command and Address...
DCS Init     Calibrating Write Level...
DCS Init     Calibrating Read DQ...
DCS Init     Calibrating Write DQ...
DCS Init     dcs_init_op_set_cal_cfg() freq_bin =%d, RL=0x%x, WL=0x%x
DCS Init     Calbration Config params init arg1=0x%x, arg2=0x%x, arg3=0x%x
dcs_init_process_condnl_block() called with condn_flag=0x%x dcs_init_loop_cnt=%d
dcs_init_op_if_density_id() called with density_id=0x%x, condn=0x%x, arg3=0x%x
dcs_init_op_if_density_id
Unsupported DCS_CONDN passed to dcs_init_op_if_density_id()!!
dcs_init_op_if_bytemode() called with bytemode=0x%x, condn=0x%x, arg3=0x%x
dcs_init_op_if_bytemode
Unsupported DCS_CONDN passed to dcs_init_op_if_bytemode()!!
dcs_init_op_if_rev_id() called with rev_id=0x%x, rev_id2=0x%x, condn=0x%x
dcs_init_op_if_rev_id
Unsupported DCS_CONDN passed to dcs_init_op_if_rev_id()!!
dcs_init_op_if_rev_id1() called with rev_id1=0x%x, condn=0x%x
, arg3=0x%x
dcs_init_op_if_rev_id1
Unsupported DCS_CONDN passed to dcs_init_op_if_rev_id1()!!
dcs_init_op_if_rev_id2() called with rev_id1=0x%x, condn=0x%x
, arg3=0x%x
dcs_init_op_if_rev_id2
Unsupported DCS_CONDN passed to dcs_init_op_if_rev_id2()!!
DCS Init     dcs_init_op_if_vendor_id() called with vendor_id=0x%x, condn=0x%x, arg3=0x%x
dcs_init_op_if_vendor_id
Unsupported DCS_CONDN passed to dcs_init_op_if_vendor_id()!!
DCS Init     dcs_init_op_if_other_id() called with other_id=0x%x, condn=0x%x, arg3=0x%x
dcs_init_op_if_other_id
Unsupported other_id passed to dcs_init_op_if_other_id()!!
Unsupported DCS_CONDN passed to dcs_init_op_if_other_id()!!
DCS Init     dcs_init_op_if_other_id() result:%x
########### Step %d. ############
DCS Init     dcs_opcode_if_num_channel() called with num_chnl=0x%x, condn=0x%x, arg3=0x%x
dcs_opcode_if_num_channel
Unsupported DCS_CONDN passed to dcs_opcode_if_num_channel()!!
DCS Init     dcs_opcode_if_soc_rev() called with soc_rev=0x%x, condn=0x%x, arg3=0x%x
dcs_init_op_if_soc_rev
dcs_init
DCS Init Config is NOT RIGHT
 DCS Op Descriptor table size grater than DCS op table size
Unsupported binning_type %u
clock-frequencies
clock-frequencies-regs
clock-frequencies-nclk
ASSERT (%llx:%d)
%s node not found
tunables not found for chip revision %x
Invalid tunables struct
number of states (%d) less than required for %s (%d)
PCPU
Binning for %s sram mode %d not found for current %s sram revision %d
voltage-states13
voltage-states13-sram
voltage-states13-extra
voltage-states5
voltage-states5-sram
voltage-states5-extra
voltage-states1
voltage-states1-sram
voltage-states1-extra
voltage-states7
voltage-states7-sram
voltage-states8
voltage-states10
voltage-states11
voltage-states12
voltage-states9
voltage-states9-sram
voltage-states14
voltage-states14-sram
voltage-states15
voltage-states15-sram
voltage-states16
voltage-states16-sram
voltage-states17
voltage-states17-sram
voltage-states18
voltage-states18-sram
voltage-states19
voltage-states19-sram
voltage-states20
voltage-states20-sram
voltage-states21
voltage-states22
voltage-states22-sram
voltage-states22-extra
voltage-states23
voltage-states23-sram
voltage-states23-extra
voltage-states30
voltage-states31
nominal-performance1
boost-performance1
total-rails-leakage
tunablerp-is-production-mode
vdd-cio-rail-always-on
perf-states-sram
perf-states-sram1
perf-states-sram2
perf-states-sram3
perf-states-sram4
perf-states-sram5
perf-states-sram6
perf-states-sram7
perf-states
perf-states1
perf-states2
perf-states3
perf-states4
perf-states5
perf-states6
perf-states7
perf-state-count
gpu-num-perf-states
perf-state-table-count
afr-perf-states
cs-perf-states
Binning type %u not supported
%s%u
No matching bridge for base address %p
Invalid pmgr_ps_fixup_type %u
arm-io/pmgr
arm-io/pmgr not found
chiplet-count
arm-io/pmgr-child
arm-io/pmgr-child not found
devices
arm-io not found
optional-bridge-mask
edt-property-ver
ps-regs
ps-groups
service
bridge-reg-index
ranges
Node %s not found
pmgr %s %#x use wrong ps group
pmgr %s %#x use forbidden ps index
pmgr %s %#x use a ps group with invalid mapping
pmgr %s %#x use a PS address %p outside the ps mapping %#llx size %#llx
%02x
sera
simetra
IMG4
IM4P
image4_callbacks.validity_cb is NULL
image %p: bdev %p type %c%c%c%c offset 0x%llx len 0x%x
target_pass_boot_manifest() used without target_init_boot_manifest()
chosen/manifest-properties
chosen/manifest-object-properties
linked list item %p corrupted
Unknown ASN1 type %llu
%c%c%c%c
pram
vram
chip-revision
fuse-revision
product
primary-calibration-matrix
device-color-policy
boot-ios-diagnostics
UpdateDeviceTree: failed to find the /chosen node
chip-config-rack
defaults
UpdateDeviceTree: failed to find the /defaults node
override-platform-only-code
sec-research-device-erm-enabled
debug-enabled
development-cert
production-cert
gid-aes-key
uid-aes-key
secure-boot
certificate-production-status
certificate-security-mode
effective-production-status-ap
effective-security-mode-ap
boot-nonce
dram-vendor
dram-vendor-id
l2-ecc-correctable-panic
no_panic_on_corr_ecc_err=1
dram-size
dram-base
dram-type
system-trusted
board-id
chip-id
unique-chip-id
unique-device-id
die-id
random-seed
firmware-version
breadcrumbs-stage-two
breadcrumbs-stage-one
system-firmware-version
iboot-stage-one-tag
iboot-stage-one-variant
iboot-stage-two-tag
boot-chime-on-last-boot
audio-handoff-status
boot-manifest-hash
trusted-boot-policy-measurement
mix-n-match-prevention-status
kernel-ctrr-to-be-enabled
recovery-snag-key-pressed
display-rotation
display-scale
display-boot-rotation
bootp-response
ipaddr
gateway
security-domain
chip-epoch
embedded-panic-log-size
extended-nvme-timeout
nvmemu-device
boot-type
remote
platform-name
t8103
network-type
ethernet
local-mac-address
compatible
Ethernet disabled
arm-io/usb-complex/usb-device
device-mac-address
baseband
battery-id
arm-io/smc/iop-smc-nub/smc-charger
boot-voltage
boot-adapter-id
boot-reason
precharge-time
precharge-battery-state
boot-battery-state
battery-factory-id
root-snapshot-name
chosen/iBoot
start-time
debug-wait-start
load-kernel-start
populate-registry-time
osenvironments
osenvironment
%s-environment
hibernate
property_name
value
parent
node
replacement
name
Kernelcache too large
Kernelcache image not valid
chosen/memory-map
BootKC
AuxKC
-entry
-virt
charger
force-usb-power
TrustCache
DeviceTree
RAMDisk
SEPFW
SEPPatches
uStuff
preoslog
BootArgs
Kernel-mach_header
AuxKC-mach_header
tGraphClient=
rd=md0
 -progress
 -restore
%s backlight-level=%d
-noprogress
debug=
sepfw-loaded
sep-boot-slot
sepfw-booted
dart-id
lp-sip0
lp-sip1
lp-sip2
lp-sip3
lp-smb0
lp-smb1
lp-smb2
lp-amnm
lp-nsih
lp-auxi
lp-auxp
lp-auxr
lp-coih
lp-atkh
lp-spih
lp-stng
lp-lpnh
lp-ronh
lp-rpnh
lp-rolp
lp-hrlp
internal-use-only-unit
engineering-use-only-unit
factory-prerelease-global-trust
sfr-internal-use-only-sw
restore-security-overrides0
restore-security-overrides1
restore-security-overrides2
restore-security-overrides3
allow-hactivation
chosen/asmb
asmb
%02X
post-upgrade
fsboot-safe
arm-io/sep/iop-sep-nub
tz0-size-set
tz1-size-set
tz0-size
tz1-size
Apple Mobile Device (Recovery Mode)
Apple Mobile Device (DFU Mode)
SDOM:%02X CPID:%04X CPRV:%02X CPFM:%02X SCEP:%02X BDID:%02X ECID:%016llX IBFL:%02X
 SRNM:[
 NONC:
 SNON:
 DCC:%X:%X:%X
iboot-handoff
ctrr
dcp-auto-boot
builtin-battery
spi_nand0
======== Start of %s serial output. ========
root rpc task
nand_syscfg
nvme_syscfg0
nor_syscfg
com.apple.os.update-
BootPreference
tcon-device-id
Tonga A1 is deprecated
debug-soc
enable-auth-debug
DispTarget522
/dcp
nvme_nand0
asio ascwrap
asio-ascwrap-tunables
cpus
max_cpus
e-core-count
p-core-count
cpus/cpu0
cpus/cpu4
state
running
clock-frequency
memory-frequency
bus-frequency
peripheral-frequency
fixed-frequency
timebase-frequency
cpu%d
usbphy-frequency
arm-io/sgx
pmgr
lpdprx_phy_efuse
lpdprx_phy1_efuse
audio-complex
ncoref-frequency
apcie
arm-io/apciec0
arm-io/apciec1
arm-io/spi1/spinor
anvram
otgphyctrl
atc0
atc1
dart-apciec0
sid-count-a0
sids-a0
sid-count
sids
dart-usb0
dead-mappings
atc-phy0
usb-otg0
usb-drd0
atc0-dpxbar
atc0-dpphy
atc0-dpin0
atc0-dpin1
apciec0
acio0
dart-acio0
dart-apciec1
dart-usb1
atc-phy1
usb-otg1
usb-drd1
atc1-dpxbar
atc1-dpphy
atc1-dpin0
atc1-dpin1
apciec1
acio1
dart-acio1
error-handler
sep-registers-accessible
iop-aop-nub
trace-buffer
ciolog
log-buffer
LPDDR4
kis-rsm
filter-data-instance-%d
dart-dcp
dart-disp0
dart-dcpext
dart-dispext0
usb0
usb1
admac
disp0
disp-ext0
dcp-ext
dispdfr
scaler
jpeg0
jpeg1
apcie0
apcie1
apcie2
dispext
nub_spmi
smc_out
smc_in
arm-io/i2c0
arm-io/i2c1
arm-io/i2c2
arm-io/i2c3
arm-io/i2c4
arm-io/usb-complex
usb-device
nvram
nor0_raw
nvram_raw
dart-ane
ANE.img4
aop/iop-aop-nub
dart-aop
AOP.img4
dart-ave
AVE.img4
gfx-asc/iop-gfx-nub
gfx-asc
GFX.img4
dart-isp
ISP.img4
sio/iop-sio-nub
dart-sio
SIO.img4
pmp/iop-pmp-nub
PMP.img4
mtr-polynom-fuse-agx
reconfig-breakpoints
No reconfig data
Unsupported mode (%d) 
Unsupported number of channels %d 
atcrt%d
atcrt-fw-personality
arm-io/acio%d
acio_hbw_fabric
hbw_fabric_tunables
acio_hi_dn_merge_fabric
hi_dn_merge_fabric_tunables
acio_hi_up_merge_fabric
hi_up_merge_fabric_tunables
acio_hi_up_rx_desc_fabric
hi_up_rx_desc_fabric_tunables
acio_hi_up_tx_data_fabric
hi_up_tx_data_fabric_tunables
acio_hi_up_tx_desc_fabric
hi_up_tx_desc_fabric_tunables
acio_hi_up_wr_fabric
hi_up_wr_fabric_tunables
acio_lbw_fabric
lbw_fabric_tunables
acio_top
top_tunables
fw_int_ctl_management
fw_int_ctl_management_tunables
acio_pcie_adapter_regs
pcie_adapter_regs_tunables
i2c0/hpmBusManager/hpm0
usbc-fw-personality-bbr
usbc-fw-personality
atcrt0
atcrt1
apcie common
apcie-common-tunables
apcie AXI2AF
apcie-axi2af-tunables
pci-bridge%d
apcie-config-tunables
pcie-rc%d
pcie-rc-tunables
pcie-rc-gen3-shadow-tunables
pcie-rc-gen4-shadow-tunables
PCIe PHY Glue Common
apcie-phy-glue-lane%d
apcie-phy-tunables
AUS_TOP
AUS_CMN_SHM
AUS_PLL_TOP
AUSPLL_TOP
AUSPLL_CORE
PCIEPLL_TOP
PCIEPLL_CORE
apcie-phy-ip-pll-tunables
pcie_auspma_rx_eq_%u
pcie_auspma_tx_shm_%u
pcie_auspma_rx_shm_%u
pcie_auspma_tx_top_%u
pcie_auspma_rx_top_%u
apcie-phy-ip-auspma-tunables
errata53_a0
atc_apcie_debug
atc-apcie-debug-tunables
atc_apcie_fabric
atc-apcie-fabric-tunables
atc_apcie_rc
atc-apcie-rc-tunables
atc_apcie_config
arm-io/apciec%d-piodma
atc_apcie_offload_engine_fabric
atc-apcie-oe-fabric-tunables
atc_apcie_apiodma
atc-apcie-apiodma-tunables
arm-io/atc-phy%d
tunable_ATC0AXI2AF
tunable_ATC_FABRIC
tunable_USB_ACIOPHY_TOP
tunable_CIO_ACIOPHY_TOP
tunable_AUS_CMN_SHM
tunable_AUS_CMN_TOP
tunable_AUSPLL_CORE
tunable_AUSPLL_TOP
tunable_CIO3PLL_CORE
tunable_CIO3PLL_TOP
tunable_CIO_CIO3PLL_TOP
tunable_USB_LN0_AUSPMA_TX_TOP
tunable_CIO_LN0_AUSPMA_TX_TOP
tunable_USB_LN0_AUSPMA_RX_TOP
tunable_CIO_LN0_AUSPMA_RX_TOP
tunable_USB_LN0_AUSPMA_RX_SHM
tunable_CIO_LN0_AUSPMA_RX_SHM
tunable_USB_LN0_AUSPMA_RX_EQ
tunable_CIO_LN0_AUSPMA_RX_EQ
tunable_DP_LN0_AUSPMA_TX_TOP
tunable_USB_LN1_AUSPMA_TX_TOP
tunable_CIO_LN1_AUSPMA_TX_TOP
tunable_USB_LN1_AUSPMA_RX_TOP
tunable_CIO_LN1_AUSPMA_RX_TOP
tunable_USB_LN1_AUSPMA_RX_SHM
tunable_CIO_LN1_AUSPMA_RX_SHM
tunable_USB_LN1_AUSPMA_RX_EQ
tunable_CIO_LN1_AUSPMA_RX_EQ
tunable_DP_LN1_AUSPMA_TX_TOP
PACC.cpm_MTR1.MTR0
PACC.cpm_MTR1.MTR1
PACC.cpm_MTR2.MTR2
EACC.cpm_MTR1.MTR0
AGX_MTR_TOP
MTR_TOP_SOC
MTR_TOP_ANE
MTR_TOP_ISP_CPU
DCS Init          Spin %d us %s
Changing Frequency to Bin %d
dcs_change_freq
Unsupported Frequency Bin Request: %d
DCS only handles 32 bit tunable size
DCS0 Tunables MCU
DCS1 Tunables MCU
DCS2 Tunables MCU
DCS3 Tunables MCU
DCS4 Tunables MCU
DCS5 Tunables MCU
DCS6 Tunables MCU
DCS7 Tunables MCU
AMCC Tunables
AMCNP Tunables
DCS Init DONE!!
calibrate_ca
Memory CA calibration: Unable to find any Vref which did not panic for channel %d
calibrate_rddq
Memory RDDQ calibration: Unable to find any Vref which did not panic for channel %d
amp_opt_center_add
Error! Non-sensical (hi < lo) in calculating center [hi==%d, lo==%d]
cacal_find_right_failing_point
Memory CA calibration: Unable to find right side failing point for channel %d
cacal_find_right_passing_point
Memory CA calibration: Unable to find passing point for all bits on the right side 
cacal_find_left_failing_point
Memory CS calibration: Unable to find failing point for all bits on the left side
Memory CA calibration: SDLL ran out of taps when trying to find left side failing point
cacal_find_left_passing_point
Memory CA calibration: Unable to find passing point for all bits on the left side 
find_center_of_eye
Memory calibration: find_center_of_eye: Left value (%d) is > right value (%d) 
wrlvlcal_push_to_0s_region
Memory Wrlvl calibration: CAWRLVL sdll reached max tap value, yet all bytes not all 0s
Memory Wrlvl calibration: DQ%d sdll reached max tap value, yet all bytes not all 0s
wrlvlcal_find_0to1_transition
Memory Wrlvl calibration: DQ%d sdll reached max tap value, yet all bytes not all 1s
wrlvlcal_find_1to0_transition
Memory Wrlvl calibration: max tap value reached, yet all bytes not back to 0s
wrdqcal_sequence
Memory WRDQ calibration: Unable to find any Vref which did not panic for channel %d
find_wrdq_center_of_eye
Memory calibration: find_wrdq_center_of_eye: Left value (0x%x) is < right value (0x%x) 
%sINTSTS(%d): 0x%08x 
AMCC NONPLANE error: %s
AMCC PLANE%d error: INTSTS 0x%016llx AFERRLOG0/1/2/3 0x%08x/0x%08x/0x%08x/0x%08x ADDR %#llx CMD/SIZE/TYPE %#x/%#x/%#x AID/TID %#x/%#x
AMC PLANE%d error: AMCC_IRERRDBG_INTSTS = 0x%llx
Unhandled AMCC interrupt
DCS CHANNEL [%d, %d] error: INTSTS 0x%08x
DCS CHANNEL[%d %d]: INTSTS 0x%08x 
Unhandled DCS interrupt: %s
gpu-fstp-downgrade
mcx-fast-pcpu-frequency
voltage-states0
gfx-fstp-harvest
voltage-states2
tunableh-board-id
tunableh-major-rev
tunableh-minor-rev
Storage task
nvme_nand%d
nvme_firmware%d
nvme_syscfg%d
nvme_efface%d
paniclog
nvme_bis
nvme_me
nvme_ean
usb_drd
======== End of %s serial output. ========
disable-boot-wdt
%llx:%d
 fp 
 lr 
 fault at 
Stacktrace:
--> 0x%016lx
user
double panic in 
iBoot Panic: %s: 
Board: %s%s:%#x 
Chip: %04x:%#02x 
Build: %s:%s
UUID: %s
force-research-policy
NUB_SPMI
NUB_SPMI1
AOP_SPMI0
AOP_SPMI1
PMU-Main
PMU-Aux
Stockholm-0
color-accuracy-index
bics-param-set
arm-io/dcp/iop-dcp-nub
DCP.img4
DispTargetZ551
DCPEXT
arm-io/dcpext/iop-dcpext-nub
DispTargetEXT
display-color-space
ARGB8101010
display-timing
usb-complex
smc/iop-smc-nub/smc-pmu
event_name-bit21
event_name-bit13
spi3/ipd
kblang-calibration
os-min-ver
********.0,0
arm-io/spi4/dp855
raw-panel-serial-number
coverglass-serial-number
j293
j313
j456
j274
j457
invalid AES key size
AES: bad arguments
image-version
cmd-results
usb rpc task
usb cmd task
usb req
vbus poll
usb vbus
usb-hi-current
usb-no-current
rsm-usb
root-live-fs
root
apfs
**********ABCDEF
ambient-light-sensor-serial-num
%02X%02X%02X%02X%02X%02X
%02X%02X%08X%02X%02X
%02X-%08X
%02X%02X%02X%02X%02X
%02X%02X%08X
%02X%08X%02X
%02X%02X%02x
%02X%02X%02X%08X
arm-io/%s
SMMU
DART
arm-io/dart-%s
smmu-tunables-instance-%d
dart-tunables-instance-%d
dart,gen3
dart,t8110
dart,t8020
dart,t6000
dart,t6710
pt-region-%u
l%u-tt-%u
dapf-instance-%u
apf-bypass-%u
inclusive-tz-range
disp0-service
dcpexpert-service
audio-features
%s-%s
EXCLAVES
NULL
RGB888
NormalModeEnable
GAPF-
wchf image too large - 0x%zx
smc/iop-smc-nub
**********abcdef
Response Queue Empty
Header mismatch
SPMI panic, controller name %s, queue index %d:  
tcon-path
firmware-ver
device-id
bundle-ver
ecid
nonce
sdom-status
prod-status
prod-fuse-value
sdom-fuse-value
security-level
allow-whitelist-disable
allowed-boot-args
pmap-io-ranges
syscfg/
syscfg-mismatch/
macaddr/
zeroes/
string/
hex/
Device Tree too large
Device Tree image not valid
gestalt-variant-id
com.apple.System.
battery-health
fm-account-masked
fm-activation-locked
fm-spkeys
fm-spstatus
ownership-warning
%#lx
GFX: Unsupported ASC ID (%llu) or NUM_ASCS (%llu)
GFX1
gpu-region-base
gpu-region-size
gptbat-ready
ttbat-phys-addr-base
gfx-shared-region-base
gfx-shared-region-size
gfx-shared-l2-region-base
gfx-shared-l2-region-size
gfx-handoff-base
gfx-handoff-size
gfx-data-base
gfx-data-size
rtkit-private-vm-region-base
rtkit-private-vm-region-size
gfx1-data-base
gfx1-data-size
gpu-supports-higher-mA
chosen/carveout-memory-map
region-id-%d
region-name-id-%d
amcc
ctrr-a
ctrr-b
ctrr-c
tz2-sram
tz2-sram-ctrl
ctrr-d
panic-region0
panic-region1
panic-region2
tz3-sram
lower-limit
upper-limit
enable
lock
write-disable
dsid-force-enable
broadcast
cache-status
master-lock
boot-image-last
AssertMacros: %s, %s file: %s, line: %d
lib/hibernation/hibernation.c
boot_image
kvs_ret == 0 
(errno == 0) && (next != cur) && (next[0] == ':') && (next[1] != 0)
(errno == 0) && (parsed_seed <= UINT_MAX) && (next != cur) && (next[0] == 0)
SPTM CTRR Key
XNU CTRR Key
hibernate_hardware_hmac_buffer(KEY_IBOOT, key_input_str, sizeof(key_input_str), output, sizeof(output))
hibernate_is_resume()
not exiting hibernation
boot_image_valid
boot-image is invalid
failed to enable decryption
failed to validate header signature (xnu panic on entry)
hibernate_gen_ctrr_key(KEY_SPTM_CTRR, sptm_ctrr_key, sizeof(sptm_ctrr_key))
failed to generate SPTM key
cc_cmp_safe(sizeof(protected_metadata_hmac), protected_metadata_hmac, local_header.protected_metadata_hmac) == 0
failed to validate protected_metadata_hmac
failed to read preview image
%s: %s
hibernate_load_image_preflight
TXM-ro
TXM-rx
TXM-bx
AuxKC-ro
AuxKC-rx
BootKC-rx
BootKC-bx
BootKC-ro
BootKC-rs
CL4-rx
CL4-ro
SPTM-ro
SPTM-rx
SPTM-rw
preflight_succeeded
preflight did not succeed
(((kc_layout) != ((void *)0)) &&((kc_layout)->present == 1) && ((kc_layout)->bx_size != 0)) == uses_sptm
kernelcache layout and header are not coherent
could not read image1
hibernate_hardware_hmac_buffer(KEY_IBOOT, hibernate_header->rorgnSHA256, sizeof(hibernate_header->rorgnSHA256), new_rorgn_hmac, sizeof(new_rorgn_hmac))
failed to compute PPL read-only region HMAC
cc_cmp_safe(sizeof(new_rorgn_hmac), new_rorgn_hmac, hibernate_header->rorgnHMAC) == 0
failed to validate PPL read-only region HMAC
hibernate_hardware_hmac_buffer(KEY_WARM_BOOT, prefix_hash, sizeof(prefix_hash), header->imageHeaderHMAC, sizeof(header->imageHeaderHMAC))
failed to compute image header hash HMAC
cc_cmp_safe(sizeof(original_hmac), original_hmac, header->imageHeaderHMAC) == 0
failed to validate image header hash HMAC
hibernate_validate_segments(segments, seg_start, image_end, segments_hmac, cc_di, cc_ctx)
failed to validate segments
hibernate_restore_segments(uses_sptm, segments, seg_start, hibtext_page, hibtext_codeOffset, &hib_entrypoint, &found_handoff)
failed to restore segments
found_handoff.bytes
failed to find iBoot handoff region
hib_entrypoint
failed to find PPL hibtext entrypoint
hibernate_populate_handoff(header, ((void *)0), header_phys, cc_di, cc_ctx)
failed to populate xnu handoff region
hibernate_load_image
hibernation image handoff is invalid
hmacsha_setup(0, &config) == 0 
hmacsha_do_hmac(0, (const uint8_t *)input, input_len, output, output_len) == 0 
pmu_get_continuous_time_offset(&rtc_offset) == 0 
pmu_set_continuous_time_offset(rtc_offset) == 0 
%s failed
hibernate_compute_clock_offset_internal
device
sep_client_get_hibernate_key(hib_sw_seed, hib_wrapped_key, &key_len) == 0 
blockdev_set_encryption_key(dev, hib_wrapped_key, hib_wrapped_key_len) == 0 
to_mem_pos >= header_size
ret == header_size
header->signature == kIOHibernateHeaderSignature
header->deviceBlockSize > header_size
header->image1Size > header_size
header->imageSize >= header->image1Size
header->imageHeaderHMACSize >= header->deviceBlockSize
header->previewSize >= header->previewPageListSize
read_ctx.cur_extent_offset <= extent_length
!os_add_overflow(extent_start, read_ctx.cur_extent_offset, &read_offset)
ret > 0
remaining == 0
read_ctx.cur_mem_pos == to_mem_pos
failed to disable decryption on device
cur_size <= xnu_handoff_alloc + xnu_handoff_start - next_handoff_addr
xnu_handoff_size <= xnu_handoff_alloc
hibernate_hardware_hmac_buffer(KEY_WARM_BOOT, handoff_hash, sizeof(handoff_hash), header->handoffHMAC, sizeof(header->handoffHMAC))
image_end - seg_pos >= (intptr_t)segment_size
hibernate_hardware_hmac_buffer(KEY_WARM_BOOT, segments_hash, sizeof(segments_hash), segments_hmac, sizeof(segments_hmac))
cc_cmp_safe(sizeof(segments_hmac), segments_hmac, hmac) == 0 
((handoff_t *)seg_pos)->region_size <= segment_size
phys_addr == memory_region_pa(memory_region)
segment_size == memory_region_size(memory_region)
phys_addr >= memory_region_pa(kern_region)
phys_addr + segment_size <= memory_region_pa_end(kern_region)
current_segment->protection == (VM_PROT_READ | VM_PROT_EXECUTE)
hibtext_codeOffset < segment_size
plane
lib/hibernation_ui/hibernation_ui.c
hib_preview.len
validate_sequence(seq, hib_preview.len, plane->width, plane->height, &images)
images
image->size.width == plane->width
image->size.height == plane->height
plane->plane_size >= bufferSize
buffer
display_dcp_create_surface(&surfaces[i], CS_ARGB8888, 1, &tmp_plane)
display_dcp_enable_normal_mode()
display_dcp_send_swap(&layer, timestamp)
size >= sizeof(hib_ui_sequence_t)
lib/hibernation_ui/hibernation_ui_types.h
seq->header.imageCount >= 1
seq->header.imageCount <= MAX_IMAGES
seq->header.width == width
seq->header.width <= MAX_WIDTH
seq->header.height == height
seq->header.height <= MAX_HEIGHT
seq->header.depth == 32
seq->magic == HIB_UI_SEQ_MAGIC
seq->version == HIB_UI_SEQ_VERSION
seq->num_frames >= 1
seq->num_frames <= MAX_FRAMES
size >= sizeof(hib_ui_sequence_t) + seq->num_frames * sizeof(hib_ui_frame_t) + seq->header.imageCount * sizeof(hib_ui_image_t)
(void *)(image->data) <= end_ptr
image->size.width <= seq->header.width
image->size.height <= seq->header.height
data_end <= end_ptr
(void *)image <= end_ptr
frame->img_index < seq->header.imageCount
frame->src_pos.x < image->size.width
frame->src_pos.y < image->size.height
frame->src_pos.x + frame->src_size.width <= image->size.width
frame->src_pos.y + frame->src_size.height <= image->size.height
frame->dst_pos.x < seq->header.width
frame->dst_pos.y < seq->header.height
frame->dst_pos.x + frame->src_size.width <= seq->header.width
frame->dst_pos.y + frame->src_size.height <= seq->header.height
frame->dst_layer <= hib_ui_layer_max
iboot-data
iboot_ifwX
iboot_nefw
Unknown
object size check failed (%zu < %zu)
rsize check failed (%zu < %zu)
size check failed (%zu < %zu)
<<FORMAT BUG! 
diuoxXncs%pfFeEgGaA
********
**********abcdef
**********
unreachable condition
%02X%02X%02X%02X-%02X%02X-%02X%02X-%02X%02X-%02X%02X%02X%02X%02X%02X
boot-object-manifests
secure-boot-hashes
/iscpreboot
/bootobjects
%s/SFR/current
apfs-preboot-uuid
boot-uuid
associated-volume-group
boot-objects-path
external-boot
 %s%s%s
%s:%s:%s
%s/%s/boot/%s
%s/%s/boot/active
/recovery
/gbosd
-rootdmg-ramdisk root-dmg=file://
rp0=file://
-bsdmgroot-ramdisk -post-upgrade
nvram-bank-size
nvram-bank-count
nvram-current-bank
nvram-proxy-data
wwwwwwwwwwww
guid-var-trans
nvram-raw
nvram,nor
%s %s = "
%%%02X
VMiBoot 
LZFSE   
iBootIm
EFI PART
Update
Ramdisk image not valid
%s/usr/standalone/firmware/FUD/%s
Unsupported number of FTAB entries
__TEXT2
__ETEXT
__EDATA
__OS_LOG
__SHARED
asc-data-isolation-
asc-data-isolation
chosen/dynamic-object-map
arm-io/sep
%s: handle create failed
%s: load failed
socd_resume
socd_push_resume
failed to read/validate the socd push buffer section descriptors
Failed to write socd dram address smc key, result %#x
Failed to write socd dram size smc key, result %#x
arm-io/smc/iop-smc-nub/smc-socd
push-buffer-address
push-buffer-size
push-section-descriptors
arm-io/smc/iop-smc-nub
failed to find the socd config parent node
smc-socd
failed to get socd push buffer base address
failed to get socd push buffer size
socd-trace-ram
failed to init socd kernel push section, result %u
 @0x%016lx
syscfg-keybag-ids
syscfg-keybag-ids-providers
syscfg-erly-kbgs
syscfg-late-kbgs
iboot-syscfg
data-class
allow-load
allow-unsealed
format
storage
load-status
syscfg-seal-keys-allowed
syscfg-seal-keys-loaded
syscfg-keybag-fallback-allowed
syscfg-keybag-fallback-used
manifest-entitlements
aptk
seal
trst
%08X-%016llX
early
main
interrupt
Apple Inc.
Samsung
Qimonda
Elpida
Etron
Nanya
Hynix
Mosel
Winbond
ESMT
Reserved
Spansion
ZMOS
Intel
Numonyx
Micron
FPGA
chosen/lock-regs
aperture-count
aperture-size
aperture-phys-addr
plane-count
plane-stride
page-size-shift
reg-offset
reg-mask
reg-value
FS task
usb_serial
Apple USB Serial Interface
%x(%x) 
BOOT
DONE
COMMIT
<%s> 
%s%s%s
Default
pre-loaded
uuid
asc-dram-mask
vm-base
vm-size
segment-names
segment-ranges
oslog-uuids
oslog-slices
routes
panic_trace
arm-io/cpu-debug-interface
panic-trace-mode
management
%s: unsupported version [0x%x -> 0x%x], expected 0x%x
syslog
crashlog
%s :%s
Unknown exception
- with nested
%s(%lu)
/%03d
 INBOX UNDERFLOW
 INBOX OVERFLOW
 OUTBOX UNDERFLOW
 OUTBOX OVERFLOW
user%02u
unknown%02u
kdebug
ioreporting
remotefs
forwarded-crashlog
core-analytics
oslog
tracekit
ACIPC-perf
timesync
entropy
  pc=%#010x  fsr=%#010x (%s)  far=%#010x 
far_physical=%#018llx 
  pc=0x%08x 
%s %u
Alignment fault
Instruction cache maintenance fault
Translation table walk synchronous external abort (1st level)
Translation table walk synchronous external abort (2nd level)
Translation table walk synchronous parity error (1st level)
Translation table walk synchronous parity error (2nd level)
Translation fault (section)
Translation fault (page)
Access Flag fault (section)
Access Flag fault (page)
Domain fault (section)
Domain fault (page)
Permission fault (section)
Permission fault (page)
Debug event
Synchronous external abort
TLB conflict abort
Implementation defined (Lockdown)
Implementation defined (Coprocessor abort)
Memory access synchronous parity error
Asynchronous external abort
Memory access asynchronous parity error
Translation fault level
Access flag fault level
Permission fault level
Synchronous external abort on translation table walk level
Synchronous parity error on memory access on translation table walk level
Domain fault level
Synchronous parity error on memory access
Asynchronous parity error on memory access
  pc=%#018llx  Exception class=%#04x (%s), IL=%u, iss=%#x far=%#018llx 
  pc=%#018llx 
Unknown reason
UNDEF exception
Unhandled SWI
PREFETCH ABORT
DATA ABORT
SERROR
PANIC
NMI FIQ
forced log msg
forced crash msg
Stack guard fault
Mailbox error
Recoverable PANIC
binlog
%s the refcon arg was NULL
DERReturn _DERDecodeSysConfig3ParseAndPopulateMetadataSequence(DERDecodedInfo *__single, _Bool *__single, void *__single)
Unable to allocate sysconfig3vX object. Likely due to an unsupported magic: 0x%X or version: 0x%X
Unable to decode payload with error %d
Tag was not the expected Payload tag %llX
Decode sequence error: %d
Unable to decode metadata with error %d
Tag was not the expected metadata tag %llX
Unable to decode manifest with error %d
Tag was not the expected manifest tag %llX
Decode manifest sequence error: %d
%s key was invalid
struct __SysConfig3PayloadEntry *__single_allocSysConfig3PayloadEntryInternal(uint32_t, const char *__single __terminated_by(0), const uint8_t *__single __counted_by(dataLength), uint32_t, const char *__single __terminated_by(0), _Bool)
%s one of data, string, or deleted must be provided
%s data, string and deleted are mutually exclusive. Caller provided parameters [%s%s%s ]
 data
 string
 deleted
%s data was 0 length
%s invalid arguments. Either entry or data was NULL
_Bool sysConfig3PayloadEntryGetData(struct __SysConfig3PayloadEntry *__single, SysConfigData *__single)
%s key must be non-NULL
struct __SysConfig3MetadataEntry *__singleallocSysConfig3MetadataEntry(const char *__single __terminated_by(0), const uint8_t *__single __counted_by(dataLength), uint32_t, const char *__single __terminated_by(0))
%s data and string are mutually exclusive
%s either data or string must be non-null
%s data cannot be length zero
%s one of the hmacs were NULL. payload ptr: %p metdata ptr: %p full ptr: %p
struct __SysConfig3ManifestEntry *__singleallocSysConfig3Manifest(SysConfig3Version, const SysConfigData *__single, const SysConfigData *__single, const SysConfigData *__single)
%s Unsupported sysconfig version: 0x%X
_Bool _sysConfig3HMACsAreEqual(SysConfig3Version, const SysConfigData *__single, const SysConfigData *__single)
%s invalid magic provided 0x%08X
struct __SysConfig3 *__singleallocSysConfig3(SysConfig3Magic, SysConfig3Version)
%s invalid version provided 0x%08X
%s invalid argument. sysconfig cannot be NULL
struct __SysConfig3PayloadEntry *__singlesysConfig3GetPayloadEntryForKey(struct __SysConfig3 *__single, uint32_t)
_Bool _sysConfig3DeletePayloadEntryForKey(struct __SysConfig3 *__single, uint32_t)
%s Failed to validate HMAC.
%s invalid argument. sysconfig and key cannot be NULL
_Bool _sysConfig3DeleteMetadataEntryForKey(struct __SysConfig3 *__single, const char *__single __terminated_by(0))
%s invalid argument. sysconfig and etnry cannot be NULL
_Bool _sysConfig3AddPayloadEntry(struct __SysConfig3 *__single, struct __SysConfig3PayloadEntry *__single)
%s invalid argument. sysconfig and entry cannot be NULL
_Bool sysConfig3AddPayloadEntry(struct __SysConfig3 *__single, struct __SysConfig3PayloadEntry *__single)
%s Payloads with the deleted flag are only supported on version 0x%08X or higher.
_Bool _sysConfig3AddMetadataEntry(struct __SysConfig3 *__single, struct __SysConfig3MetadataEntry *__single)
%s sysconfig (0x%p) or manifest (0x%p) argument was null
_Bool _sysConfig3SetManifest(struct __SysConfig3 *__single, struct __SysConfig3ManifestEntry *__single)
%s sysconfig version (0X%08X) did not match manifest version (0X%08X
%s source string was NULL
char *__single __terminated_by(0)_allocAndCopyString(const char *__single __terminated_by(0), size_t)
%s data was zero length
uint8_t *__indexable_allocAndCopyData(const uint8_t *__single __counted_by(dataLength), uint32_t)
%s sourceData or destData was NULL
_Bool _copySysConfigData(const SysConfigData *__single, SysConfigData *__single)
%s sourceData was 0 length
%s sysconfig is NULL.
_Bool sysConfig3IsValidHMAC(struct __SysConfig3 *__single)
Invalid HMAC key. sysconfig->hmacKey.data = %p, sysconfig->hmacKey.length = %d. Did you register HMAC key?
%s Payload HMAC does not match.
%s Metadata HMAC does not match.
%s Full HMAC does not match.
%s Invalid HAMC key. sysconfig->hmacKey.data = %p, sysconfig->hmacKey.length = %u
_Bool sysConfig3CreatePayloadHMAC(struct __SysConfig3 *__single, SysConfigData *__single)
%s hmac is NULL
%s malloc failed.
_Bool sysConfig3CreateMetadataHMAC(struct __SysConfig3 *__single, SysConfigData *__single)
_Bool sysConfig3CreateFullHMAC(struct __SysConfig3 *__single, SysConfigData *__single)
%s Invalid HMAC key. sysconfig->hmacKey.data = %p, sysconfig->hmacKey.length = %u
alloc ws
free ws
ADDA
gfCf
acsf
gfCd
acsf
gfC0suafsuaf
laes
suaf
vppa
suaf
gfCeacseacse
foic
fumt
fsna
i2c2
3@Uf
2@3D
r"$3
2"#C
r"$3
 `Zm
 `Zm
 `Zm
 `Zm
``HH
PP66
=XK]
??K]

 2`l
 2`l
TT
@T@T
@T@T
@T@T
@T@T
@T@T
TT
TT
uoui
uoue
tgpf
soui
0osr
1osr
2osr
3osr
lCPDprimary-calibration-matrix
lCLGgamma-calibration-lut
lCBDdisplay-backlight-compensation
lCLBbacklight-calibration
NULL
avsp
avip
ooipsoip
NULL
GCLC
/221xkcd
y6.:
1wfi
2wfi
tz0-size-override
tz1-size-override
1rdf
2rdf
main
interrupt
atsC
YAPS
ATEM
INAM
