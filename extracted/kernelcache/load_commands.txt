extracted/kernelcache/kernelcache_payload:
Load command 0
     cmd LC_UUID
 cmdsize 24
    uuid 19A9C3A7-9A94-6B0B-1E10-0E4B2EEB00CF
Load command 1
      cmd LC_BUILD_VERSION
  cmdsize 24
 platform 0
    minos 0.0
      sdk n/a
   ntools 0
Load command 2
        cmd LC_UNIXTHREAD
    cmdsize 288
     flavor ARM_THREAD_STATE64
      count ARM_THREAD_STATE64_COUNT
	    x0  0x0000000000000000 x1  0x0000000000000000 x2  0x0000000000000000
	    x3  0x0000000000000000 x4  0x0000000000000000 x5  0x0000000000000000
	    x6  0x0000000000000000 x7  0x0000000000000000 x8  0x0000000000000000
	    x9  0x0000000000000000 x10 0x0000000000000000 x11 0x0000000000000000
	    x12 0x0000000000000000 x13 0x0000000000000000 x14 0x0000000000000000
	    x15 0x0000000000000000 x16 0x0000000000000000 x17 0x0000000000000000
	    x18 0x0000000000000000 x19 0x0000000000000000 x20 0x0000000000000000
	    x21 0x0000000000000000 x22 0x0000000000000000 x23 0x0000000000000000
	    x24 0x0000000000000000 x25 0x0000000000000000 x26 0x0000000000000000
	    x27 0x0000000000000000 x28 0x0000000000000000  fp 0x0000000000000000
	     lr 0x0000000000000000 sp  0x0000000000000000  pc 0xfffffe000c028000
	   cpsr 0x00000000
Load command 3
      cmd LC_DYLD_CHAINED_FIXUPS
  cmdsize 16
  dataoff 117309440
 datasize 2136
Load command 4
      cmd LC_SEGMENT_64
  cmdsize 72
  segname __TEXT
   vmaddr 0xfffffe0007004000
   vmsize 0x0000000000008000
  fileoff 0
 filesize 32768
  maxprot 0x00000001
 initprot 0x00000001
   nsects 0
    flags 0x0
Load command 5
      cmd LC_SEGMENT_64
  cmdsize 152
  segname __PRELINK_TEXT
   vmaddr 0xfffffe000700c000
   vmsize 0x0000000000c0c000
  fileoff 32768
 filesize 12632064
  maxprot 0x00000001
 initprot 0x00000001
   nsects 1
    flags 0x0
Section
  sectname __text
   segname __PRELINK_TEXT
      addr 0xfffffe000700c000
      size 0x0000000000c0c000
    offset 32768
     align 2^0 (1)
    reloff 0
    nreloc 0
     flags 0x80000400
 reserved1 0
 reserved2 0
Load command 6
      cmd LC_SEGMENT_64
  cmdsize 72
  segname __DATA_CONST
   vmaddr 0xfffffe0007c18000
   vmsize 0x0000000000b2c000
  fileoff 12664832
 filesize 11714560
  maxprot 0x00000001
 initprot 0x00000001
   nsects 0
    flags 0x0
Load command 7
      cmd LC_SEGMENT_64
  cmdsize 72
  segname __DATA_SPTM
   vmaddr 0xfffffe0008744000
   vmsize 0x0000000000054000
  fileoff 24379392
 filesize 344064
  maxprot 0x00000001
 initprot 0x00000001
   nsects 0
    flags 0x0
Load command 8
      cmd LC_SEGMENT_64
  cmdsize 72
  segname __TEXT_EXEC
   vmaddr 0xfffffe0008798000
   vmsize 0x0000000003890000
  fileoff 24723456
 filesize 59310080
  maxprot 0x00000005
 initprot 0x00000005
   nsects 0
    flags 0x0
Load command 9
      cmd LC_SEGMENT_64
  cmdsize 72
  segname __TEXT_BOOT_EXEC
   vmaddr 0xfffffe000c028000
   vmsize 0x0000000000008000
  fileoff 84033536
 filesize 32768
  maxprot 0x00000005
 initprot 0x00000005
   nsects 0
    flags 0x0
Load command 10
      cmd LC_SEGMENT_64
  cmdsize 152
  segname __PRELINK_INFO
   vmaddr 0xfffffe000c030000
   vmsize 0x00000000003b4000
  fileoff 84066304
 filesize 3883008
  maxprot 0x00000003
 initprot 0x00000003
   nsects 1
    flags 0x0
Section
  sectname __info
   segname __PRELINK_INFO
      addr 0xfffffe000c030000
      size 0x00000000003b4000
    offset 84066304
     align 2^0 (1)
    reloff 0
    nreloc 0
     flags 0x00000000
 reserved1 0
 reserved2 0
Load command 11
      cmd LC_SEGMENT_64
  cmdsize 72
  segname __DATA
   vmaddr 0xfffffe000c3e4000
   vmsize 0x0000000000410000
  fileoff 87949312
 filesize 4259840
  maxprot 0x00000003
 initprot 0x00000003
   nsects 0
    flags 0x0
Load command 12
      cmd LC_SEGMENT_64
  cmdsize 72
  segname __LINKEDIT
   vmaddr 0xfffffe000c7f4000
   vmsize 0x00000000017f4000
  fileoff 92209152
 filesize 25116672
  maxprot 0x00000001
 initprot 0x00000001
   nsects 0
    flags 0x0
Load command 13
       cmd LC_FILESET_ENTRY
   cmdsize 56
    vmaddr 0xfffffe000700c000
   fileoff 32768
  entry_id com.apple.kernel (offset 32)
  reserved 0
Load command 14
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000711c000
   fileoff 1146880
  entry_id com.apple.AGXFirmwareKextG15CRTBuddy (offset 32)
  reserved 0
Load command 15
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000711ccd0
   fileoff 1150160
  entry_id com.apple.AGXFirmwareKextRTBuddy64 (offset 32)
  reserved 0
Load command 16
       cmd LC_FILESET_ENTRY
   cmdsize 56
    vmaddr 0xfffffe0007120cd0
   fileoff 1166544
  entry_id com.apple.AGXG15C (offset 32)
  reserved 0
Load command 17
       cmd LC_FILESET_ENTRY
   cmdsize 48
    vmaddr 0xfffffe00071374f0
   fileoff 1258736
  entry_id com.apple.AUC (offset 32)
  reserved 0
Load command 18
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00071384d0
   fileoff 1262800
  entry_id com.apple.driver.AppleA7IOP-ASCWrap-v6 (offset 32)
  reserved 0
Load command 19
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007138fb0
   fileoff 1265584
  entry_id com.apple.driver.AppleA7IOP-MXWrap-v1 (offset 32)
  reserved 0
Load command 20
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007139930
   fileoff 1268016
  entry_id com.apple.driver.AppleA7IOP (offset 32)
  reserved 0
Load command 21
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000713ae20
   fileoff 1273376
  entry_id com.apple.driver.AppleAHCIPort (offset 32)
  reserved 0
Load command 22
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000713def0
   fileoff 1285872
  entry_id com.apple.driver.AppleALSColorSensor (offset 32)
  reserved 0
Load command 23
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007141e20
   fileoff 1302048
  entry_id com.apple.driver.AppleANELoadBalancer (offset 32)
  reserved 0
Load command 24
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007146560
   fileoff 1320288
  entry_id com.apple.driver.AppleAOPAudio (offset 32)
  reserved 0
Load command 25
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007153360
   fileoff 1373024
  entry_id com.apple.driver.AppleAOPVoiceTrigger (offset 32)
  reserved 0
Load command 26
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007156ff0
   fileoff 1388528
  entry_id com.apple.iokit.AppleARMIISAudio (offset 32)
  reserved 0
Load command 27
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000715ce90
   fileoff 1412752
  entry_id com.apple.driver.AppleARMPMU (offset 32)
  reserved 0
Load command 28
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007160300
   fileoff 1426176
  entry_id com.apple.driver.AppleARMPlatform (offset 32)
  reserved 0
Load command 29
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00071705b0
   fileoff 1492400
  entry_id com.apple.driver.AppleARMWatchdogTimer (offset 32)
  reserved 0
Load command 30
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007172200
   fileoff 1499648
  entry_id com.apple.driver.AppleAVD (offset 32)
  reserved 0
Load command 31
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00071fe6a0
   fileoff 2074272
  entry_id com.apple.driver.AppleAVE2 (offset 32)
  reserved 0
Load command 32
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00072c57b0
   fileoff 2889648
  entry_id com.apple.driver.AppleActuatorDriver (offset 32)
  reserved 0
Load command 33
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00072c7430
   fileoff 2896944
  entry_id com.apple.driver.AppleAudioClockLibs (offset 32)
  reserved 0
Load command 34
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00072c7c70
   fileoff 2899056
  entry_id com.apple.driver.AppleAuthCP (offset 32)
  reserved 0
Load command 35
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00072cbfd0
   fileoff 2916304
  entry_id com.apple.driver.AppleBCMWLANBusInterfacePCIe (offset 32)
  reserved 0
Load command 36
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00072e3830
   fileoff 3012656
  entry_id com.apple.driver.AppleBCMWLANCore (offset 32)
  reserved 0
Load command 37
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000735a8f0
   fileoff 3500272
  entry_id com.apple.driver.AppleBSDKextStarter (offset 32)
  reserved 0
Load command 38
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000735af80
   fileoff 3501952
  entry_id com.apple.driver.AppleBTM (offset 32)
  reserved 0
Load command 39
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007360210
   fileoff 3523088
  entry_id com.apple.driver.AppleBiometricSensor (offset 32)
  reserved 0
Load command 40
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000736b9a0
   fileoff 3570080
  entry_id com.apple.driver.AppleBiometricServices (offset 32)
  reserved 0
Load command 41
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000736c6a0
   fileoff 3573408
  entry_id com.apple.driver.AppleBluetoothDebug (offset 32)
  reserved 0
Load command 42
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe000736e630
   fileoff 3581488
  entry_id com.apple.driver.AppleBluetoothDebugService (offset 32)
  reserved 0
Load command 43
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000736ec40
   fileoff 3583040
  entry_id com.apple.driver.AppleBluetoothModule (offset 32)
  reserved 0
Load command 44
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007371870
   fileoff 3594352
  entry_id com.apple.driver.AppleBluetoothMultitouch (offset 32)
  reserved 0
Load command 45
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007373db0
   fileoff 3603888
  entry_id com.apple.driver.AppleBluetoothRemote (offset 32)
  reserved 0
Load command 46
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007375ec0
   fileoff 3612352
  entry_id com.apple.driver.AppleCSEmbeddedAudio (offset 32)
  reserved 0
Load command 47
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007376d40
   fileoff 3616064
  entry_id com.apple.driver.AppleCallbackPowerSource (offset 32)
  reserved 0
Load command 48
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00073783b0
   fileoff 3621808
  entry_id com.apple.driver.AppleConvergedIPCOLYBTControl (offset 32)
  reserved 0
Load command 49
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007380c20
   fileoff 3656736
  entry_id com.apple.driver.AppleConvergedPCI (offset 32)
  reserved 0
Load command 50
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073886a0
   fileoff 3688096
  entry_id com.apple.driver.AppleCredentialManager (offset 32)
  reserved 0
Load command 51
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00073a2380
   fileoff 3793792
  entry_id com.apple.driver.AppleDAPF (offset 32)
  reserved 0
Load command 52
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00073a2d00
   fileoff 3796224
  entry_id com.apple.driver.AppleDCP (offset 32)
  reserved 0
Load command 53
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073a4ef0
   fileoff 3804912
  entry_id com.apple.driver.AppleDCPDPTXProxy (offset 32)
  reserved 0
Load command 54
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073a6fe0
   fileoff 3813344
  entry_id com.apple.driver.AppleDPDisplayTCON (offset 32)
  reserved 0
Load command 55
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073a8700
   fileoff 3819264
  entry_id com.apple.driver.AppleDPRepeater (offset 32)
  reserved 0
Load command 56
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00073ac300
   fileoff 3834624
  entry_id com.apple.driver.AppleDPTX (offset 32)
  reserved 0
Load command 57
       cmd LC_FILESET_ENTRY
   cmdsize 88
    vmaddr 0xfffffe00073b8090
   fileoff 3883152
  entry_id com.apple.driver.AppleDiagnosticDataAccessReadOnly (offset 32)
  reserved 0
Load command 58
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00073b88a0
   fileoff 3885216
  entry_id com.apple.driver.AppleDialogPMU (offset 32)
  reserved 0
Load command 59
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073b94c0
   fileoff 3888320
  entry_id com.apple.driver.AppleDiskImages2 (offset 32)
  reserved 0
Load command 60
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073bd280
   fileoff 3904128
  entry_id com.apple.driver.AppleDisplayCrossbar (offset 32)
  reserved 0
Load command 61
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073c8130
   fileoff 3948848
  entry_id com.apple.driver.AppleDisplayManager (offset 32)
  reserved 0
Load command 62
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073c8e50
   fileoff 3952208
  entry_id com.apple.driver.AppleDockChannel (offset 32)
  reserved 0
Load command 63
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00073ca9f0
   fileoff 3959280
  entry_id com.apple.driver.AppleEffaceableBlockDevice (offset 32)
  reserved 0
Load command 64
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073cb130
   fileoff 3961136
  entry_id com.apple.driver.AppleEffaceableNOR (offset 32)
  reserved 0
Load command 65
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073cb880
   fileoff 3963008
  entry_id com.apple.driver.AppleEffaceableStorage (offset 32)
  reserved 0
Load command 66
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073cd390
   fileoff 3969936
  entry_id com.apple.driver.AppleEffaceableTDM (offset 32)
  reserved 0
Load command 67
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073cda20
   fileoff 3971616
  entry_id com.apple.driver.AppleEmbeddedAudio (offset 32)
  reserved 0
Load command 68
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073d5870
   fileoff 4003952
  entry_id com.apple.driver.AppleCS42L84Audio (offset 32)
  reserved 0
Load command 69
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073db0d0
   fileoff 4026576
  entry_id com.apple.driver.AppleSN012776Amp (offset 32)
  reserved 0
Load command 70
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073dbb60
   fileoff 4029280
  entry_id com.apple.driver.AppleEmbeddedAudioLibs (offset 32)
  reserved 0
Load command 71
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00073e0fd0
   fileoff 4050896
  entry_id com.apple.driver.AppleEmbeddedLightSensor (offset 32)
  reserved 0
Load command 72
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073e3fd0
   fileoff 4063184
  entry_id com.apple.driver.AppleEmbeddedPCIE (offset 32)
  reserved 0
Load command 73
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00073ee180
   fileoff 4104576
  entry_id com.apple.AppleEmbeddedSimpleSPINORFlasher (offset 32)
  reserved 0
Load command 74
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00073efb80
   fileoff 4111232
  entry_id com.apple.driver.AppleEmbeddedTempSensor (offset 32)
  reserved 0
Load command 75
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073f2ee0
   fileoff 4124384
  entry_id com.apple.driver.AppleEmbeddedUSBHost (offset 32)
  reserved 0
Load command 76
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073f3b00
   fileoff 4127488
  entry_id com.apple.kec.AppleEncryptedArchive (offset 32)
  reserved 0
Load command 77
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073f4020
   fileoff 4128800
  entry_id com.apple.driver.AppleEventLogHandler (offset 32)
  reserved 0
Load command 78
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00073f4ce0
   fileoff 4132064
  entry_id com.apple.driver.AppleEverestErrorHandler (offset 32)
  reserved 0
Load command 79
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073f6180
   fileoff 4137344
  entry_id com.apple.driver.AppleFDEKeyStore (offset 32)
  reserved 0
Load command 80
       cmd LC_FILESET_ENTRY
   cmdsize 96
    vmaddr 0xfffffe00073f6c60
   fileoff 4140128
  entry_id com.apple.AppleFSCompression.AppleFSCompressionTypeDataless (offset 32)
  reserved 0
Load command 81
       cmd LC_FILESET_ENTRY
   cmdsize 88
    vmaddr 0xfffffe00073f7770
   fileoff 4142960
  entry_id com.apple.AppleFSCompression.AppleFSCompressionTypeZlib (offset 32)
  reserved 0
Load command 82
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073fb790
   fileoff 4159376
  entry_id com.apple.driver.AppleFileSystemDriver (offset 32)
  reserved 0
Load command 83
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00073fc030
   fileoff 4161584
  entry_id com.apple.driver.AppleFirmwareKit (offset 32)
  reserved 0
Load command 84
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007400d10
   fileoff 4181264
  entry_id com.apple.driver.AppleFirmwareUpdateKext (offset 32)
  reserved 0
Load command 85
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007401c50
   fileoff 4185168
  entry_id com.apple.driver.AppleGPIOICController (offset 32)
  reserved 0
Load command 86
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007403280
   fileoff 4190848
  entry_id com.apple.driver.AppleGameControllerPersonality (offset 32)
  reserved 0
Load command 87
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007403a40
   fileoff 4192832
  entry_id com.apple.driver.AppleH11ANEInterface (offset 32)
  reserved 0
Load command 88
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007445730
   fileoff 4462384
  entry_id com.apple.driver.AppleH13CameraInterface (offset 32)
  reserved 0
Load command 89
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000745b2b0
   fileoff 4551344
  entry_id com.apple.driver.AppleH15MCD (offset 32)
  reserved 0
Load command 90
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007465580
   fileoff 4593024
  entry_id com.apple.driver.AppleHIDALSService (offset 32)
  reserved 0
Load command 91
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007465d30
   fileoff 4594992
  entry_id com.apple.driver.AppleHIDKeyboard (offset 32)
  reserved 0
Load command 92
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00074673e0
   fileoff 4600800
  entry_id com.apple.driver.AppleBluetoothHIDKeyboard (offset 32)
  reserved 0
Load command 93
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007467ad0
   fileoff 4602576
  entry_id com.apple.driver.AppleHIDMouse (offset 32)
  reserved 0
Load command 94
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007468830
   fileoff 4606000
  entry_id com.apple.driver.AppleBluetoothHIDMouse (offset 32)
  reserved 0
Load command 95
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007469440
   fileoff 4609088
  entry_id com.apple.driver.AppleUSBHIDMouse (offset 32)
  reserved 0
Load command 96
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000746a030
   fileoff 4612144
  entry_id com.apple.driver.AppleHIDTransport (offset 32)
  reserved 0
Load command 97
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007477270
   fileoff 4665968
  entry_id com.apple.driver.AppleHIDTransportFIFO (offset 32)
  reserved 0
Load command 98
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe000747a460
   fileoff 4678752
  entry_id com.apple.driver.AppleHIDTransportSCMCommon (offset 32)
  reserved 0
Load command 99
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000747b160
   fileoff 4682080
  entry_id com.apple.driver.AppleHIDTransportSPI (offset 32)
  reserved 0
Load command 100
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007483980
   fileoff 4716928
  entry_id com.apple.driver.AppleHPM (offset 32)
  reserved 0
Load command 101
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007496b90
   fileoff 4795280
  entry_id com.apple.driver.AppleHighVoltageCharger (offset 32)
  reserved 0
Load command 102
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00074985e0
   fileoff 4802016
  entry_id com.apple.driver.AppleIISController (offset 32)
  reserved 0
Load command 103
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007499640
   fileoff 4806208
  entry_id com.apple.driver.AppleIPAppender (offset 32)
  reserved 0
Load command 104
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000749a380
   fileoff 4809600
  entry_id com.apple.security.AppleImage4 (offset 32)
  reserved 0
Load command 105
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00074ab220
   fileoff 4878880
  entry_id com.apple.driver.AppleInputDeviceSupport (offset 32)
  reserved 0
Load command 106
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00074ade40
   fileoff 4890176
  entry_id com.apple.driver.AppleInterruptControllerV3 (offset 32)
  reserved 0
Load command 107
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00074aef50
   fileoff 4894544
  entry_id com.apple.driver.AppleJPEGDriver (offset 32)
  reserved 0
Load command 108
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00074bce20
   fileoff 4951584
  entry_id com.apple.driver.AppleLMBacklight (offset 32)
  reserved 0
Load command 109
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00074bd910
   fileoff 4954384
  entry_id com.apple.driver.AppleLSIFusionMPT (offset 32)
  reserved 0
Load command 110
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00074c15f0
   fileoff 4969968
  entry_id com.apple.driver.AppleLockdownMode (offset 32)
  reserved 0
Load command 111
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00074c6710
   fileoff 4990736
  entry_id com.apple.driver.AppleM2ScalerCSCDriver (offset 32)
  reserved 0
Load command 112
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000754c4a0
   fileoff 5538976
  entry_id com.apple.driver.AppleM68Buttons (offset 32)
  reserved 0
Load command 113
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007552320
   fileoff 5563168
  entry_id com.apple.driver.AppleMCA2-T603x (offset 32)
  reserved 0
Load command 114
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007558910
   fileoff 5589264
  entry_id com.apple.kext.AppleMatch (offset 32)
  reserved 0
Load command 115
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007558e60
   fileoff 5590624
  entry_id com.apple.driver.AppleMesaSEPDriver (offset 32)
  reserved 0
Load command 116
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007561e90
   fileoff 5627536
  entry_id com.apple.driver.AppleMobileApNonce (offset 32)
  reserved 0
Load command 117
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007563de0
   fileoff 5635552
  entry_id com.apple.driver.AppleMobileDispT603C-DCP (offset 32)
  reserved 0
Load command 118
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe000756bfb0
   fileoff 5668784
  entry_id com.apple.driver.AppleMobileFileIntegrity (offset 32)
  reserved 0
Load command 119
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00075894f0
   fileoff 5788912
  entry_id com.apple.driver.AppleMultiFunctionManager (offset 32)
  reserved 0
Load command 120
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000758c0f0
   fileoff 5800176
  entry_id com.apple.driver.AppleMultitouchDriver (offset 32)
  reserved 0
Load command 121
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007591c10
   fileoff 5823504
  entry_id com.apple.driver.AppleNANDConfigAccess (offset 32)
  reserved 0
Load command 122
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007592230
   fileoff 5825072
  entry_id com.apple.driver.AppleOLYHAL (offset 32)
  reserved 0
Load command 123
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007597a30
   fileoff 5847600
  entry_id com.apple.driver.AppleOnboardSerial (offset 32)
  reserved 0
Load command 124
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007599b10
   fileoff 5856016
  entry_id com.apple.driver.ApplePIODMA (offset 32)
  reserved 0
Load command 125
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000759ab40
   fileoff 5860160
  entry_id com.apple.driver.ApplePMGR (offset 32)
  reserved 0
Load command 126
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00075aa790
   fileoff 5924752
  entry_id com.apple.driver.ApplePMP (offset 32)
  reserved 0
Load command 127
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00075ac310
   fileoff 5931792
  entry_id com.apple.driver.ApplePMPFirmware (offset 32)
  reserved 0
Load command 128
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00075acd00
   fileoff 5934336
  entry_id com.apple.driver.ApplePTD (offset 32)
  reserved 0
Load command 129
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00075aee10
   fileoff 5942800
  entry_id com.apple.driver.ApplePassthroughPPM (offset 32)
  reserved 0
Load command 130
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00075c03d0
   fileoff 6013904
  entry_id com.apple.driver.AppleProResHW (offset 32)
  reserved 0
Load command 131
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00075cc270
   fileoff 6062704
  entry_id com.apple.driver.AppleQSPIMC (offset 32)
  reserved 0
Load command 132
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00075cda90
   fileoff 6068880
  entry_id com.apple.driver.AppleRAID (offset 32)
  reserved 0
Load command 133
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00075d0270
   fileoff 6079088
  entry_id com.apple.driver.AppleRSMChannel (offset 32)
  reserved 0
Load command 134
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00075d0f00
   fileoff 6082304
  entry_id com.apple.driver.AppleS5L8920XPWM (offset 32)
  reserved 0
Load command 135
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00075d1a00
   fileoff 6085120
  entry_id com.apple.driver.AppleS5L8940XI2C (offset 32)
  reserved 0
Load command 136
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00075d2960
   fileoff 6089056
  entry_id com.apple.driver.AppleS5L8960XNCO (offset 32)
  reserved 0
Load command 137
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00075d3040
   fileoff 6090816
  entry_id com.apple.driver.AppleS8000AES (offset 32)
  reserved 0
Load command 138
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00075d4a70
   fileoff 6097520
  entry_id com.apple.driver.AppleS8000DWI (offset 32)
  reserved 0
Load command 139
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00075d52b0
   fileoff 6099632
  entry_id com.apple.driver.AppleSART (offset 32)
  reserved 0
Load command 140
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00075d6620
   fileoff 6104608
  entry_id com.apple.driver.AppleSCDriver (offset 32)
  reserved 0
Load command 141
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00075e1480
   fileoff 6149248
  entry_id com.apple.driver.AppleSDXC (offset 32)
  reserved 0
Load command 142
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00075e5a20
   fileoff 6167072
  entry_id com.apple.iokit.AppleSEPGenericTransfer (offset 32)
  reserved 0
Load command 143
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00075e6d90
   fileoff 6172048
  entry_id com.apple.driver.AppleSEPHDCPManager (offset 32)
  reserved 0
Load command 144
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00075e8170
   fileoff 6177136
  entry_id com.apple.driver.AppleSEPKeyStore (offset 32)
  reserved 0
Load command 145
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00075ed710
   fileoff 6199056
  entry_id com.apple.driver.AppleSEPManager (offset 32)
  reserved 0
Load command 146
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00075fa900
   fileoff 6252800
  entry_id com.apple.driver.AppleSMC (offset 32)
  reserved 0
Load command 147
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007604170
   fileoff 6291824
  entry_id com.apple.driver.AppleSPIMC (offset 32)
  reserved 0
Load command 148
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007605a90
   fileoff 6298256
  entry_id com.apple.driver.AppleSPMI (offset 32)
  reserved 0
Load command 149
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00076089a0
   fileoff 6310304
  entry_id com.apple.driver.AppleSPMIPMU (offset 32)
  reserved 0
Load command 150
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000760b670
   fileoff 6321776
  entry_id com.apple.driver.AppleSPU (offset 32)
  reserved 0
Load command 151
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00076114a0
   fileoff 6345888
  entry_id com.apple.security.SecureRemotePassword (offset 32)
  reserved 0
Load command 152
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007613ed0
   fileoff 6356688
  entry_id com.apple.driver.AppleSSE (offset 32)
  reserved 0
Load command 153
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007615d90
   fileoff 6364560
  entry_id com.apple.driver.AppleSamsungSerial (offset 32)
  reserved 0
Load command 154
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007616530
   fileoff 6366512
  entry_id com.apple.driver.AppleSerialShim (offset 32)
  reserved 0
Load command 155
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007616d00
   fileoff 6368512
  entry_id com.apple.driver.AppleSmartBatteryManager (offset 32)
  reserved 0
Load command 156
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000761f630
   fileoff 6403632
  entry_id com.apple.driver.AppleSmartIO2 (offset 32)
  reserved 0
Load command 157
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007624480
   fileoff 6423680
  entry_id com.apple.driver.AppleStockholmControl (offset 32)
  reserved 0
Load command 158
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007628ca0
   fileoff 6442144
  entry_id com.apple.driver.AppleUSBCardReader (offset 32)
  reserved 0
Load command 159
       cmd LC_FILESET_ENTRY
   cmdsize 88
    vmaddr 0xfffffe000762a640
   fileoff 6448704
  entry_id com.apple.driver.AppleUSBMassStorageInterfaceNub (offset 32)
  reserved 0
Load command 160
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000762aed0
   fileoff 6450896
  entry_id com.apple.driver.AppleUSBODD (offset 32)
  reserved 0
Load command 161
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000762b680
   fileoff 6452864
  entry_id com.apple.driver.AppleUSBTDM (offset 32)
  reserved 0
Load command 162
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000762c4a0
   fileoff 6456480
  entry_id com.apple.driver.AppleSummitLCD (offset 32)
  reserved 0
Load command 163
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe000762d9d0
   fileoff 6461904
  entry_id com.apple.driver.AppleSyntheticGameController (offset 32)
  reserved 0
Load command 164
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000762eb60
   fileoff 6466400
  entry_id com.apple.AppleSystemPolicy (offset 32)
  reserved 0
Load command 165
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007631d30
   fileoff 6479152
  entry_id com.apple.driver.AppleT6020PCIeCPIODMA (offset 32)
  reserved 0
Load command 166
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007632d70
   fileoff 6483312
  entry_id com.apple.driver.AppleT6020PCIePIODMA (offset 32)
  reserved 0
Load command 167
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007633e20
   fileoff 6487584
  entry_id com.apple.driver.AppleT6031ANEHAL (offset 32)
  reserved 0
Load command 168
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007634ce0
   fileoff 6491360
  entry_id com.apple.driver.AppleT6031CLPC (offset 32)
  reserved 0
Load command 169
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007639850
   fileoff 6510672
  entry_id com.apple.driver.AppleT6031PCIe (offset 32)
  reserved 0
Load command 170
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000763c7b0
   fileoff 6522800
  entry_id com.apple.driver.AppleT6031PMGR (offset 32)
  reserved 0
Load command 171
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000763ea30
   fileoff 6531632
  entry_id com.apple.driver.AppleT6031SOCTuner (offset 32)
  reserved 0
Load command 172
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000763f560
   fileoff 6534496
  entry_id com.apple.driver.AppleT6032CLPC (offset 32)
  reserved 0
Load command 173
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00076444c0
   fileoff 6554816
  entry_id com.apple.driver.AppleT6032PCIeC (offset 32)
  reserved 0
Load command 174
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007645020
   fileoff 6557728
  entry_id com.apple.driver.AppleT8110DART (offset 32)
  reserved 0
Load command 175
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007647fe0
   fileoff 6569952
  entry_id com.apple.driver.AppleT8122PCIeC (offset 32)
  reserved 0
Load command 176
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00076535a0
   fileoff 6616480
  entry_id com.apple.driver.AppleTCA7408GPIOIC (offset 32)
  reserved 0
Load command 177
       cmd LC_FILESET_ENTRY
   cmdsize 88
    vmaddr 0xfffffe0007653ec0
   fileoff 6618816
  entry_id com.apple.driver.AppleThunderboltDPAdapterFamily (offset 32)
  reserved 0
Load command 178
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007661cb0
   fileoff 6675632
  entry_id com.apple.driver.AppleThunderboltDPInAdapter (offset 32)
  reserved 0
Load command 179
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00076644e0
   fileoff 6685920
  entry_id com.apple.driver.AppleThunderboltDPOutAdapter (offset 32)
  reserved 0
Load command 180
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007681300
   fileoff 6804224
  entry_id com.apple.driver.AppleThunderboltEDMSource (offset 32)
  reserved 0
Load command 181
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007682f30
   fileoff 6811440
  entry_id com.apple.driver.AppleThunderboltIP (offset 32)
  reserved 0
Load command 182
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00076af8f0
   fileoff 6994160
  entry_id com.apple.driver.AppleThunderboltNHI (offset 32)
  reserved 0
Load command 183
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00076ea3a0
   fileoff 7234464
  entry_id com.apple.driver.AppleThunderboltPCIDownAdapter (offset 32)
  reserved 0
Load command 184
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00076ebc00
   fileoff 7240704
  entry_id com.apple.driver.AppleThunderboltPCIUpAdapter (offset 32)
  reserved 0
Load command 185
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00076f0410
   fileoff 7259152
  entry_id com.apple.driver.AppleThunderboltUSBDownAdapter (offset 32)
  reserved 0
Load command 186
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00076f0fb0
   fileoff 7262128
  entry_id com.apple.driver.AppleThunderboltUSBUpAdapter (offset 32)
  reserved 0
Load command 187
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00076f44e0
   fileoff 7275744
  entry_id com.apple.driver.AppleThunderboltUTDM (offset 32)
  reserved 0
Load command 188
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00076f58c0
   fileoff 7280832
  entry_id com.apple.driver.AppleTopCaseHIDEventDriver (offset 32)
  reserved 0
Load command 189
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00076f8010
   fileoff 7290896
  entry_id com.apple.driver.AppleUSBTopCaseDriver (offset 32)
  reserved 0
Load command 190
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00076f87f0
   fileoff 7292912
  entry_id com.apple.driver.AppleTrustedAccessory (offset 32)
  reserved 0
Load command 191
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007700640
   fileoff 7325248
  entry_id com.apple.driver.AppleTypeCPhy (offset 32)
  reserved 0
Load command 192
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007703600
   fileoff 7337472
  entry_id com.apple.driver.AppleT6032TypeCPhy (offset 32)
  reserved 0
Load command 193
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007712170
   fileoff 7397744
  entry_id com.apple.driver.AppleT8122TypeCPhy (offset 32)
  reserved 0
Load command 194
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007729cc0
   fileoff 7494848
  entry_id com.apple.driver.AppleTypeCRetimer (offset 32)
  reserved 0
Load command 195
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000772c290
   fileoff 7504528
  entry_id com.apple.driver.AppleUIO (offset 32)
  reserved 0
Load command 196
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000772d2f0
   fileoff 7508720
  entry_id com.apple.driver.usb.cdc.acm (offset 32)
  reserved 0
Load command 197
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000772db30
   fileoff 7510832
  entry_id com.apple.driver.AppleUSBAudio (offset 32)
  reserved 0
Load command 198
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00077312b0
   fileoff 7525040
  entry_id com.apple.driver.usb.cdc (offset 32)
  reserved 0
Load command 199
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007731aa0
   fileoff 7527072
  entry_id com.apple.driver.usb.AppleUSBCommon (offset 32)
  reserved 0
Load command 200
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007732530
   fileoff 7529776
  entry_id com.apple.driver.usb.AppleUSBVHCICommon (offset 32)
  reserved 0
Load command 201
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007733900
   fileoff 7534848
  entry_id com.apple.driver.usb.AppleUSBVHCICommonRSM (offset 32)
  reserved 0
Load command 202
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007735b20
   fileoff 7543584
  entry_id com.apple.driver.AppleUSBDeviceMux (offset 32)
  reserved 0
Load command 203
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007737500
   fileoff 7550208
  entry_id com.apple.driver.AppleUSBDeviceNCM (offset 32)
  reserved 0
Load command 204
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007738690
   fileoff 7554704
  entry_id com.apple.driver.usb.cdc.ecm (offset 32)
  reserved 0
Load command 205
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00077390f0
   fileoff 7557360
  entry_id com.apple.driver.usb.ethernet.asix (offset 32)
  reserved 0
Load command 206
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe000773a7c0
   fileoff 7563200
  entry_id com.apple.macos.driver.AppleUSBEthernetHost (offset 32)
  reserved 0
Load command 207
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe000773b9b0
   fileoff 7567792
  entry_id com.apple.driver.AppleUSBLightningAdapter (offset 32)
  reserved 0
Load command 208
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000773e160
   fileoff 7577952
  entry_id com.apple.driver.usb.cdc.ncm (offset 32)
  reserved 0
Load command 209
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000773f600
   fileoff 7583232
  entry_id com.apple.driver.usb.networking (offset 32)
  reserved 0
Load command 210
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000773fe50
   fileoff 7585360
  entry_id com.apple.driver.usb.realtek8153patcher (offset 32)
  reserved 0
Load command 211
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007742750
   fileoff 7595856
  entry_id com.apple.driver.usb.serial (offset 32)
  reserved 0
Load command 212
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007742fe0
   fileoff 7598048
  entry_id com.apple.driver.AppleUVDM (offset 32)
  reserved 0
Load command 213
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007743a10
   fileoff 7600656
  entry_id com.apple.driver.AppleUVDMDriver (offset 32)
  reserved 0
Load command 214
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007745410
   fileoff 7607312
  entry_id com.apple.driver.AppleXsanScheme (offset 32)
  reserved 0
Load command 215
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007745e10
   fileoff 7609872
  entry_id com.apple.driver.AudioDMAController-T603x (offset 32)
  reserved 0
Load command 216
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00077498e0
   fileoff 7624928
  entry_id com.apple.driver.BCMWLANFirmware4388.Hashstore (offset 32)
  reserved 0
Load command 217
       cmd LC_FILESET_ENTRY
   cmdsize 56
    vmaddr 0xfffffe000775e020
   fileoff 7708704
  entry_id com.apple.BootCache (offset 32)
  reserved 0
Load command 218
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000775ff00
   fileoff 7716608
  entry_id com.apple.security.BootPolicy (offset 32)
  reserved 0
Load command 219
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007761d70
   fileoff 7724400
  entry_id com.apple.kec.Compression (offset 32)
  reserved 0
Load command 220
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007762260
   fileoff 7725664
  entry_id com.apple.iokit.CoreAnalyticsFamily (offset 32)
  reserved 0
Load command 221
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00077660d0
   fileoff 7741648
  entry_id com.apple.driver.CoreKDL (offset 32)
  reserved 0
Load command 222
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007768150
   fileoff 7749968
  entry_id com.apple.driver.CoreStorage (offset 32)
  reserved 0
Load command 223
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00077719e0
   fileoff 7789024
  entry_id com.apple.driver.CoreStorageFsck (offset 32)
  reserved 0
Load command 224
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007773390
   fileoff 7795600
  entry_id com.apple.kext.CoreTrust (offset 32)
  reserved 0
Load command 225
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00077774b0
   fileoff 7812272
  entry_id com.apple.driver.DCPAVFamilyProxy (offset 32)
  reserved 0
Load command 226
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000777b5b0
   fileoff 7828912
  entry_id com.apple.driver.DCPDPFamilyProxy (offset 32)
  reserved 0
Load command 227
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000777c190
   fileoff 7831952
  entry_id com.apple.EXBrightCalibrationConsumer (offset 32)
  reserved 0
Load command 228
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000777cff0
   fileoff 7835632
  entry_id com.apple.iokit.EndpointSecurity (offset 32)
  reserved 0
Load command 229
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000778a2c0
   fileoff 7889600
  entry_id com.apple.driver.ExclavesAudioKext (offset 32)
  reserved 0
Load command 230
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000778e080
   fileoff 7905408
  entry_id com.apple.driver.FairPlayIOKit (offset 32)
  reserved 0
Load command 231
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00077ccb00
   fileoff 8162048
  entry_id com.apple.filesystems.hfs.kext (offset 32)
  reserved 0
Load command 232
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00077d9410
   fileoff 8213520
  entry_id com.apple.filesystems.hfs.encodings.kext (offset 32)
  reserved 0
Load command 233
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00077d9f70
   fileoff 8216432
  entry_id com.apple.driver.IISAudioIsolatedStreamECProxy (offset 32)
  reserved 0
Load command 234
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00077daac0
   fileoff 8219328
  entry_id com.apple.iokit.IO80211Family (offset 32)
  reserved 0
Load command 235
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007886eb0
   fileoff 8924848
  entry_id com.apple.iokit.IOAHCIFamily (offset 32)
  reserved 0
Load command 236
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000788bf30
   fileoff 8945456
  entry_id com.apple.iokit.IOAHCIBlockStorage (offset 32)
  reserved 0
Load command 237
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00078924c0
   fileoff 8971456
  entry_id com.apple.iokit.IOAVBFamily (offset 32)
  reserved 0
Load command 238
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000789ed60
   fileoff 9022816
  entry_id com.apple.plugin.IOAVBControlPlugin (offset 32)
  reserved 0
Load command 239
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00078a1740
   fileoff 9033536
  entry_id com.apple.plugin.IOAVBDiscoveryPlugin (offset 32)
  reserved 0
Load command 240
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00078a40b0
   fileoff 9044144
  entry_id com.apple.plugin.IOAVBStreamingPlugin (offset 32)
  reserved 0
Load command 241
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00078ab930
   fileoff 9074992
  entry_id com.apple.plugin.IOMRPPlugin (offset 32)
  reserved 0
Load command 242
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00078b18a0
   fileoff 9099424
  entry_id com.apple.iokit.IOAVFamily (offset 32)
  reserved 0
Load command 243
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00078d6880
   fileoff 9250944
  entry_id com.apple.iokit.IOAccessoryManager (offset 32)
  reserved 0
Load command 244
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00078f8ac0
   fileoff 9390784
  entry_id com.apple.iokit.IOAccessoryPortUSB (offset 32)
  reserved 0
Load command 245
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00078f96f0
   fileoff 9393904
  entry_id com.apple.iokit.IOAudio2Family (offset 32)
  reserved 0
Load command 246
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00078fa0e0
   fileoff 9396448
  entry_id com.apple.iokit.IOAudioFamily (offset 32)
  reserved 0
Load command 247
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007900430
   fileoff 9421872
  entry_id com.apple.iokit.IOBDStorageFamily (offset 32)
  reserved 0
Load command 248
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007900c60
   fileoff 9423968
  entry_id com.apple.iokit.IOBiometricFamily (offset 32)
  reserved 0
Load command 249
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00079042b0
   fileoff 9437872
  entry_id com.apple.iokit.IOBluetoothFamily (offset 32)
  reserved 0
Load command 250
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007912ef0
   fileoff 9498352
  entry_id com.apple.driver.IOBluetoothHIDDriver (offset 32)
  reserved 0
Load command 251
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe000791ad30
   fileoff 9530672
  entry_id com.apple.iokit.IOBufferCopyEngineFamily (offset 32)
  reserved 0
Load command 252
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000791bc20
   fileoff 9534496
  entry_id com.apple.iokit.IOCDStorageFamily (offset 32)
  reserved 0
Load command 253
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000791c5f0
   fileoff 9537008
  entry_id com.apple.iokit.IOCECFamily (offset 32)
  reserved 0
Load command 254
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe000791d030
   fileoff 9539632
  entry_id com.apple.iokit.IOCryptoAcceleratorFamily (offset 32)
  reserved 0
Load command 255
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000791dbf0
   fileoff 9542640
  entry_id com.apple.driver.IODARTFamily (offset 32)
  reserved 0
Load command 256
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007920190
   fileoff 9552272
  entry_id com.apple.iokit.IODVDStorageFamily (offset 32)
  reserved 0
Load command 257
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00079209a0
   fileoff 9554336
  entry_id com.apple.iokit.IODisplayPortFamily (offset 32)
  reserved 0
Load command 258
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007933d10
   fileoff 9633040
  entry_id com.apple.iokit.IOGPUFamily (offset 32)
  reserved 0
Load command 259
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000793f3e0
   fileoff 9679840
  entry_id com.apple.iokit.IOGameControllerFamily (offset 32)
  reserved 0
Load command 260
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00079428e0
   fileoff 9693408
  entry_id com.apple.iokit.IOGraphicsFamily (offset 32)
  reserved 0
Load command 261
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007947120
   fileoff 9711904
  entry_id com.apple.iokit.IOHDCPFamily (offset 32)
  reserved 0
Load command 262
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe000794d420
   fileoff 9737248
  entry_id com.apple.driver.DiskImages (offset 32)
  reserved 0
Load command 263
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe000794f2f0
   fileoff 9745136
  entry_id com.apple.driver.DiskImages.FileBackingStore (offset 32)
  reserved 0
Load command 264
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe000794fb00
   fileoff 9747200
  entry_id com.apple.driver.DiskImages.KernelBacked (offset 32)
  reserved 0
Load command 265
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007950850
   fileoff 9750608
  entry_id com.apple.driver.DiskImages.RAMBackingStore (offset 32)
  reserved 0
Load command 266
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007950ef0
   fileoff 9752304
  entry_id com.apple.driver.DiskImages.ReadWriteDiskImage (offset 32)
  reserved 0
Load command 267
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007951510
   fileoff 9753872
  entry_id com.apple.driver.DiskImages.UDIFDiskImage (offset 32)
  reserved 0
Load command 268
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007954ef0
   fileoff 9768688
  entry_id com.apple.iokit.IOHIDFamily (offset 32)
  reserved 0
Load command 269
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000795ff20
   fileoff 9813792
  entry_id com.apple.driver.IOHIDPowerSource (offset 32)
  reserved 0
Load command 270
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007961220
   fileoff 9818656
  entry_id com.apple.driver.IOImageLoader (offset 32)
  reserved 0
Load command 271
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00079637d0
   fileoff 9828304
  entry_id com.apple.iokit.IOKitRegistryCompatibility (offset 32)
  reserved 0
Load command 272
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007963e80
   fileoff 9830016
  entry_id com.apple.iokit.IOMobileGraphicsFamily-DCP (offset 32)
  reserved 0
Load command 273
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe000796b650
   fileoff 9860688
  entry_id com.apple.iokit.IOMobileGraphicsFamily (offset 32)
  reserved 0
Load command 274
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007974d30
   fileoff 9899312
  entry_id com.apple.iokit.IONVMeFamily (offset 32)
  reserved 0
Load command 275
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007984630
   fileoff 9963056
  entry_id com.apple.iokit.IONetworkingFamily (offset 32)
  reserved 0
Load command 276
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007985f50
   fileoff 9969488
  entry_id com.apple.iokit.AppleBCM5701Ethernet (offset 32)
  reserved 0
Load command 277
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe000798a880
   fileoff 9988224
  entry_id com.apple.driver.AppleEthernetAquantiaAqtion (offset 32)
  reserved 0
Load command 278
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007991e40
   fileoff 10018368
  entry_id com.apple.AppleEthernetAquantiaAqtionFirmware (offset 32)
  reserved 0
Load command 279
       cmd LC_FILESET_ENTRY
   cmdsize 88
    vmaddr 0xfffffe0007992860
   fileoff 10020960
  entry_id com.apple.driver.AppleEthernetAquantiaAqtionPortMonitor (offset 32)
  reserved 0
Load command 280
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007993230
   fileoff 10023472
  entry_id com.apple.driver.AppleI2CEthernetAquantia (offset 32)
  reserved 0
Load command 281
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007994c40
   fileoff 10030144
  entry_id com.apple.driver.mDNSOffloadUserClient (offset 32)
  reserved 0
Load command 282
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007996560
   fileoff 10036576
  entry_id com.apple.iokit.IOPCIFamily (offset 32)
  reserved 0
Load command 283
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00079a3790
   fileoff 10090384
  entry_id com.apple.iokit.IOPortFamily (offset 32)
  reserved 0
Load command 284
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00079a7790
   fileoff 10106768
  entry_id com.apple.iokit.IORSMFamily (offset 32)
  reserved 0
Load command 285
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00079a7ff0
   fileoff 10108912
  entry_id com.apple.iokit.IOReportFamily (offset 32)
  reserved 0
Load command 286
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00079a8b60
   fileoff 10111840
  entry_id com.apple.iokit.IOSCSIArchitectureModelFamily (offset 32)
  reserved 0
Load command 287
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00079ab140
   fileoff 10121536
  entry_id com.apple.iokit.IOSCSIBlockCommandsDevice (offset 32)
  reserved 0
Load command 288
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00079acf80
   fileoff 10129280
  entry_id com.apple.iokit.IOSCSIMultimediaCommandsDevice (offset 32)
  reserved 0
Load command 289
       cmd LC_FILESET_ENTRY
   cmdsize 88
    vmaddr 0xfffffe00079ae650
   fileoff 10135120
  entry_id com.apple.iokit.IOSCSIReducedBlockCommandsDevice (offset 32)
  reserved 0
Load command 290
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00079af160
   fileoff 10137952
  entry_id com.apple.iokit.SCSITaskUserClient (offset 32)
  reserved 0
Load command 291
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00079afa00
   fileoff 10140160
  entry_id com.apple.iokit.IOSCSIParallelFamily (offset 32)
  reserved 0
Load command 292
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00079b0fe0
   fileoff 10145760
  entry_id com.apple.iokit.IOSerialFamily (offset 32)
  reserved 0
Load command 293
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00079b1db0
   fileoff 10149296
  entry_id com.apple.iokit.IOSkywalkFamily (offset 32)
  reserved 0
Load command 294
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00079b4e60
   fileoff 10161760
  entry_id com.apple.driver.IOSlaveProcessor (offset 32)
  reserved 0
Load command 295
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe00079b5570
   fileoff 10163568
  entry_id com.apple.iokit.IOSlowAdaptiveClockingFamily (offset 32)
  reserved 0
Load command 296
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00079b62d0
   fileoff 10166992
  entry_id com.apple.iokit.IOStorageFamily (offset 32)
  reserved 0
Load command 297
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00079b83d0
   fileoff 10175440
  entry_id com.apple.iokit.IOStreamFamily (offset 32)
  reserved 0
Load command 298
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe00079b8bd0
   fileoff 10177488
  entry_id com.apple.iokit.IOSurface (offset 32)
  reserved 0
Load command 299
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00079beef0
   fileoff 10202864
  entry_id com.apple.IOTextEncryptionFamily (offset 32)
  reserved 0
Load command 300
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe00079bf780
   fileoff 10205056
  entry_id com.apple.iokit.IOThunderboltFamily (offset 32)
  reserved 0
Load command 301
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007a26010
   fileoff 10625040
  entry_id com.apple.iokit.IOTimeSyncFamily (offset 32)
  reserved 0
Load command 302
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007a32e90
   fileoff 10677904
  entry_id com.apple.plugin.IOgPTPPlugin (offset 32)
  reserved 0
Load command 303
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007a57600
   fileoff 10827264
  entry_id com.apple.iokit.IOUSBDeviceFamily (offset 32)
  reserved 0
Load command 304
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007a5c6d0
   fileoff 10847952
  entry_id com.apple.driver.AppleT7000USBOTGDevice (offset 32)
  reserved 0
Load command 305
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007a61440
   fileoff 10867776
  entry_id com.apple.driver.AppleUSBXDCI (offset 32)
  reserved 0
Load command 306
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007a69b10
   fileoff 10902288
  entry_id com.apple.driver.AppleUSBXDCIARM (offset 32)
  reserved 0
Load command 307
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007a72770
   fileoff 10938224
  entry_id com.apple.iokit.IOUSBHostFamily (offset 32)
  reserved 0
Load command 308
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007a85d70
   fileoff 11017584
  entry_id com.apple.driver.usb.AppleEmbeddedUSBXHCIPCI (offset 32)
  reserved 0
Load command 309
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007a87c60
   fileoff 11025504
  entry_id com.apple.driver.usb.AppleSynopsysUSB40XHCI (offset 32)
  reserved 0
Load command 310
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007a94f80
   fileoff 11079552
  entry_id com.apple.driver.usb.AppleSynopsysUSBXHCI (offset 32)
  reserved 0
Load command 311
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007a9e4a0
   fileoff 11117728
  entry_id com.apple.driver.usb.AppleUSBEHCI (offset 32)
  reserved 0
Load command 312
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007aa6bb0
   fileoff 11152304
  entry_id com.apple.driver.usb.AppleUSBEHCIPCI (offset 32)
  reserved 0
Load command 313
       cmd LC_FILESET_ENTRY
   cmdsize 88
    vmaddr 0xfffffe0007aa84f0
   fileoff 11158768
  entry_id com.apple.driver.usb.AppleUSBHostBillboardDevice (offset 32)
  reserved 0
Load command 314
       cmd LC_FILESET_ENTRY
   cmdsize 88
    vmaddr 0xfffffe0007aa8e70
   fileoff 11161200
  entry_id com.apple.driver.usb.AppleUSBHostCompositeDevice (offset 32)
  reserved 0
Load command 315
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007aa9ae0
   fileoff 11164384
  entry_id com.apple.driver.AppleUSBHostMergeProperties (offset 32)
  reserved 0
Load command 316
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007aaa1a0
   fileoff 11166112
  entry_id com.apple.driver.usb.AppleUSBHostPacketFilter (offset 32)
  reserved 0
Load command 317
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007aab390
   fileoff 11170704
  entry_id com.apple.driver.usb.AppleUSBHostiOSDevice (offset 32)
  reserved 0
Load command 318
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007aac190
   fileoff 11174288
  entry_id com.apple.driver.usb.AppleUSBHub (offset 32)
  reserved 0
Load command 319
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007ab0610
   fileoff 11191824
  entry_id com.apple.driver.AppleUSBMergeNub (offset 32)
  reserved 0
Load command 320
       cmd LC_FILESET_ENTRY
   cmdsize 80
    vmaddr 0xfffffe0007ab0c30
   fileoff 11193392
  entry_id com.apple.driver.usb.AppleUSBRecoveryHost (offset 32)
  reserved 0
Load command 321
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007ab2850
   fileoff 11200592
  entry_id com.apple.driver.usb.AppleUSBUserHCI (offset 32)
  reserved 0
Load command 322
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007ab6870
   fileoff 11217008
  entry_id com.apple.driver.usb.AppleUSBVHCI (offset 32)
  reserved 0
Load command 323
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007abb0e0
   fileoff 11235552
  entry_id com.apple.driver.usb.AppleUSBVHCIRSM (offset 32)
  reserved 0
Load command 324
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007abc3a0
   fileoff 11240352
  entry_id com.apple.driver.usb.AppleUSBXHCI (offset 32)
  reserved 0
Load command 325
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007ac7010
   fileoff 11284496
  entry_id com.apple.driver.usb.AppleUSBXHCIPCI (offset 32)
  reserved 0
Load command 326
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007acbdf0
   fileoff 11304432
  entry_id com.apple.driver.usb.IOUSBHostHIDDevice (offset 32)
  reserved 0
Load command 327
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007acd890
   fileoff 11311248
  entry_id com.apple.iokit.IOUSBMassStorageDriver (offset 32)
  reserved 0
Load command 328
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007ad0f80
   fileoff 11325312
  entry_id com.apple.iokit.IOUserEthernet (offset 32)
  reserved 0
Load command 329
       cmd LC_FILESET_ENTRY
   cmdsize 72
    vmaddr 0xfffffe0007ad2290
   fileoff 11330192
  entry_id com.apple.driver.driverkit.serial (offset 32)
  reserved 0
Load command 330
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007ad2c70
   fileoff 11332720
  entry_id com.apple.iokit.IOVideoFamily (offset 32)
  reserved 0
Load command 331
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007ad3480
   fileoff 11334784
  entry_id com.apple.kec.InvalidateHmac (offset 32)
  reserved 0
Load command 332
       cmd LC_FILESET_ENTRY
   cmdsize 56
    vmaddr 0xfffffe0007ad43b0
   fileoff 11338672
  entry_id com.apple.nke.l2tp (offset 32)
  reserved 0
Load command 333
       cmd LC_FILESET_ENTRY
   cmdsize 56
    vmaddr 0xfffffe0007ad54c0
   fileoff 11343040
  entry_id com.apple.kec.Libm (offset 32)
  reserved 0
Load command 334
       cmd LC_FILESET_ENTRY
   cmdsize 56
    vmaddr 0xfffffe0007adc540
   fileoff 11371840
  entry_id com.apple.nke.ppp (offset 32)
  reserved 0
Load command 335
       cmd LC_FILESET_ENTRY
   cmdsize 56
    vmaddr 0xfffffe0007ade6b0
   fileoff 11380400
  entry_id com.apple.nke.pppoe (offset 32)
  reserved 0
Load command 336
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007adf610
   fileoff 11384336
  entry_id com.apple.security.quarantine (offset 32)
  reserved 0
Load command 337
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007ae0590
   fileoff 11388304
  entry_id com.apple.driver.RTBuddy (offset 32)
  reserved 0
Load command 338
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007aeac90
   fileoff 11431056
  entry_id com.apple.driver.SCDPProxy (offset 32)
  reserved 0
Load command 339
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007aec950
   fileoff 11438416
  entry_id com.apple.driver.SEPHibernation (offset 32)
  reserved 0
Load command 340
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007aeda60
   fileoff 11442784
  entry_id com.apple.security.sandbox (offset 32)
  reserved 0
Load command 341
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007b16230
   fileoff 11608624
  entry_id com.apple.driver.SoftRAID (offset 32)
  reserved 0
Load command 342
       cmd LC_FILESET_ENTRY
   cmdsize 56
    vmaddr 0xfffffe0007b19160
   fileoff 11620704
  entry_id com.apple.UVCService (offset 32)
  reserved 0
Load command 343
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007b19860
   fileoff 11622496
  entry_id com.apple.filesystems.acfs (offset 32)
  reserved 0
Load command 344
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007b546f0
   fileoff 11863792
  entry_id com.apple.filesystems.acfsctl (offset 32)
  reserved 0
Load command 345
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007b54df0
   fileoff 11865584
  entry_id com.apple.filesystems.afpfs (offset 32)
  reserved 0
Load command 346
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007b61c00
   fileoff 11918336
  entry_id com.apple.filesystems.apfs (offset 32)
  reserved 0
Load command 347
       cmd LC_FILESET_ENTRY
   cmdsize 56
    vmaddr 0xfffffe0007bb7270
   fileoff 12268144
  entry_id com.apple.nke.asp_tcp (offset 32)
  reserved 0
Load command 348
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007bba180
   fileoff 12280192
  entry_id com.apple.filesystems.autofs (offset 32)
  reserved 0
Load command 349
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007bbb8b0
   fileoff 12286128
  entry_id com.apple.filesystems.cd9660 (offset 32)
  reserved 0
Load command 350
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007bbd170
   fileoff 12292464
  entry_id com.apple.filesystems.cddafs (offset 32)
  reserved 0
Load command 351
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007bbe200
   fileoff 12296704
  entry_id com.apple.driver.corecapture (offset 32)
  reserved 0
Load command 352
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007bc4dc0
   fileoff 12324288
  entry_id com.apple.kec.corecrypto (offset 32)
  reserved 0
Load command 353
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007bdeb20
   fileoff 12430112
  entry_id com.apple.filesystems.exfat (offset 32)
  reserved 0
Load command 354
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007be03c0
   fileoff 12436416
  entry_id com.apple.driver.iPodSBCDriver (offset 32)
  reserved 0
Load command 355
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007be0a80
   fileoff 12438144
  entry_id com.apple.filesystems.lifs (offset 32)
  reserved 0
Load command 356
       cmd LC_FILESET_ENTRY
   cmdsize 56
    vmaddr 0xfffffe0007be4a30
   fileoff 12454448
  entry_id com.apple.kext.mcx.alr (offset 32)
  reserved 0
Load command 357
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007be5f50
   fileoff 12459856
  entry_id com.apple.filesystems.msdosfs (offset 32)
  reserved 0
Load command 358
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007be7640
   fileoff 12465728
  entry_id com.apple.filesystems.nfs (offset 32)
  reserved 0
Load command 359
       cmd LC_FILESET_ENTRY
   cmdsize 56
    vmaddr 0xfffffe0007bf1d60
   fileoff 12508512
  entry_id com.apple.kec.pthread (offset 32)
  reserved 0
Load command 360
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007bf2ac0
   fileoff 12511936
  entry_id com.apple.filesystems.smbfs (offset 32)
  reserved 0
Load command 361
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007c0e450
   fileoff 12624976
  entry_id com.apple.filesystems.tmpfs (offset 32)
  reserved 0
Load command 362
       cmd LC_FILESET_ENTRY
   cmdsize 56
    vmaddr 0xfffffe0007c0f180
   fileoff 12628352
  entry_id com.apple.kext.triggers (offset 32)
  reserved 0
Load command 363
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007c0fb50
   fileoff 12630864
  entry_id com.apple.filesystems.udf (offset 32)
  reserved 0
Load command 364
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007c14ce0
   fileoff 12651744
  entry_id com.apple.nke.webcontentfilter (offset 32)
  reserved 0
Load command 365
       cmd LC_FILESET_ENTRY
   cmdsize 64
    vmaddr 0xfffffe0007c165e0
   fileoff 12658144
  entry_id com.apple.filesystems.webdav (offset 32)
  reserved 0
